/* Base styles and mobile-first approach */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    font-size: 16px;
}

/* Container styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Button styles */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 44px;
    line-height: 1.2;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

/* Disabled button styles */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed !important;
    pointer-events: none;
    transform: none !important;
    box-shadow: none !important;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Button spinner for loading states */
.btn-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
    margin-right: 4px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Submit button specific styles */
.btn-primary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-primary:disabled:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #1e7e34;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-outline {
    background-color: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background-color: #007bff;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Card styles */
.card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card h3 {
    margin-bottom: 16px;
    color: #2c3e50;
    font-size: 18px;
}

/* Message styles */
.message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: none;
    font-weight: 500;
}

.message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 24px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #000;
}

/* Success Modal Styles */
.success-modal {
    max-width: 600px;
    text-align: center;
}

.success-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    margin: -24px -24px 24px -24px;
    padding: 24px;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.success-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.success-header .modal-close {
    position: absolute;
    top: 12px;
    right: 16px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.success-header .modal-close:hover {
    opacity: 1;
}

.success-icon {
    margin: 16px 0 24px 0;
}

.success-icon svg {
    filter: drop-shadow(0 4px 8px rgba(40, 167, 69, 0.3));
}

.success-message {
    margin-bottom: 24px;
}

.success-message p {
    font-size: 1.1rem;
    margin-bottom: 20px;
    color: #495057;
}

.investment-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 16px 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
    color: #28a745;
}

.detail-label {
    color: #6c757d;
    font-weight: 500;
}

.detail-value {
    color: #495057;
    font-weight: 600;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 24px;
}

.modal-footer .btn {
    flex: 1;
    max-width: 200px;
}

/* Table styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
}

table tr:hover {
    background-color: #f8f9fa;
}

/* Pagination styles */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 16px 0;
}

.pagination button {
    margin: 0 8px;
}

/* Loading styles */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-success {
    color: #28a745;
}

.text-danger {
    color: #dc3545;
}

.text-warning {
    color: #ffc107;
}

.text-info {
    color: #17a2b8;
}

.text-muted {
    color: #6c757d;
}

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.d-none {
    display: none !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: flex !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

/* QR Code Styles */
.qr-code-display {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin: 15px 0;
}

.qr-image-container {
    position: relative;
    display: inline-block;
}

.qr-code-image {
    max-width: 220px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: opacity 0.3s ease;
}

.qr-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
}

.qr-info {
    margin-top: 15px;
}

.qr-info p {
    margin: 5px 0;
    color: #555;
}

.qr-amount {
    font-weight: 600;
    font-size: 16px;
    color: #28a745;
}

.qr-address-short {
    font-family: monospace;
    font-size: 14px;
    color: #666;
}

.qr-note {
    font-size: 12px;
    color: #888;
    font-style: italic;
}

.qr-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
    flex-wrap: wrap;
}

.qr-error {
    text-align: center;
    padding: 20px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    color: #721c24;
}

.manual-address {
    margin-top: 15px;
}

.manual-address input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-family: monospace;
    font-size: 14px;
    margin-bottom: 10px;
}

@media (max-width: 768px) {
    .qr-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .qr-code-image {
        max-width: 180px;
    }
}

/* Responsive breakpoints */
@media (min-width: 576px) {
    .container {
        padding: 0 24px;
    }
    
    .modal-content {
        margin: 15% auto;
        width: 80%;
    }
}

@media (min-width: 768px) {
    body {
        font-size: 14px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
        min-height: 40px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 14px;
        font-size: 14px;
    }
    
    .card {
        padding: 32px;
    }
    
    .modal-content {
        width: 60%;
        max-width: 600px;
    }
}

@media (min-width: 992px) {
    .container {
        padding: 0 32px;
    }
    
    .modal-content {
        width: 50%;
    }
}

@media (min-width: 1200px) {
    .container {
        padding: 0 40px;
    }
}

/* Deposit Success Modal Styles */
.deposit-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
    animation: fadeIn 0.3s ease-out;
}

.deposit-success-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease-out;
}

.deposit-success-modal .success-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 24px;
    border-radius: 12px 12px 0 0;
    text-align: center;
    position: relative;
}

.deposit-success-modal .success-header h3 {
    margin: 8px 0 0 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.deposit-success-modal .success-icon {
    font-size: 3rem;
    margin-bottom: 8px;
    display: block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.deposit-success-modal .success-content {
    padding: 24px;
    text-align: center;
}

.deposit-success-modal .success-content p {
    margin: 12px 0;
    font-size: 1rem;
    color: #495057;
    line-height: 1.5;
}

.deposit-success-modal .success-content p:last-child {
    margin-top: 20px;
    font-style: italic;
    color: #6c757d;
}

.deposit-success-modal .success-actions {
    padding: 0 24px 24px 24px;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .deposit-success-overlay {
        padding: 10px;
    }
    
    .deposit-success-modal .success-actions {
        flex-direction: column;
    }
    
    .deposit-success-modal .success-actions .btn {
        flex: none;
        width: 100%;
    }
}
