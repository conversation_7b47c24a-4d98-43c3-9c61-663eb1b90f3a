/* Mobile-First Authentication Styles for TLS Wallet */

/* CSS Variables for Mobile Auth */
:root {
    /* Mobile-specific colors */
    --mobile-primary: #667eea;
    --mobile-primary-dark: #5a6fd8;
    --mobile-secondary: #764ba2;
    --mobile-accent: #38a169;
    --mobile-danger: #e53e3e;
    --mobile-warning: #ed8936;
    --mobile-info: #3182ce;
    
    /* Mobile gradients */
    --mobile-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --mobile-card-gradient: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
    --mobile-glass-bg: rgba(255, 255, 255, 0.1);
    --mobile-glass-border: rgba(255, 255, 255, 0.2);
    
    /* Mobile spacing */
    --mobile-padding: 20px;
    --mobile-gap: 16px;
    --mobile-border-radius: 16px;
    --mobile-input-height: 56px;
    --mobile-button-height: 52px;
    
    /* Mobile typography */
    --mobile-font-size: 16px;
    --mobile-font-weight: 500;
    --mobile-line-height: 1.5;
    
    /* Mobile shadows */
    --mobile-shadow-light: 0 4px 20px rgba(0, 0, 0, 0.1);
    --mobile-shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.15);
    --mobile-shadow-strong: 0 16px 48px rgba(0, 0, 0, 0.2);
    
    /* Touch targets */
    --touch-target: 44px;
    --touch-padding: 12px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --mobile-bg-gradient: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        --mobile-card-gradient: linear-gradient(135deg, rgba(26,32,44,0.95) 0%, rgba(45,55,72,0.9) 100%);
        --mobile-glass-bg: rgba(0, 0, 0, 0.3);
        --mobile-glass-border: rgba(255, 255, 255, 0.1);
    }
}

/* Base Mobile Styles */
body {
    font-size: var(--mobile-font-size);
    line-height: var(--mobile-line-height);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    touch-action: manipulation;
}

/* Mobile Navigation */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--mobile-glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--mobile-glass-border);
    padding: env(safe-area-inset-top) 0 0 0;
}

.mobile-nav .nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px var(--mobile-padding);
    min-height: 60px;
}

.mobile-nav .nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.mobile-nav .brand-icon {
    width: 40px;
    height: 40px;
    background: var(--mobile-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: var(--mobile-shadow-light);
}

.mobile-nav .brand-text h2 {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mobile-nav .brand-subtitle {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.mobile-nav .nav-actions {
    display: flex;
    gap: 8px;
}

.mobile-nav .nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: var(--touch-target);
    min-height: var(--touch-target);
}

.mobile-nav .nav-link:hover {
    background: var(--mobile-glass-bg);
    color: white;
    transform: translateY(-1px);
}

/* Mobile Auth Container */
.mobile-auth-container {
    min-height: 100vh;
    background: var(--mobile-bg-gradient);
    padding: 80px var(--mobile-padding) var(--mobile-padding);
    padding-bottom: env(safe-area-inset-bottom);
    position: relative;
    overflow-x: hidden;
}

.mobile-auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
    pointer-events: none;
}

.mobile-auth-container .auth-wrapper {
    position: relative;
    z-index: 1;
    max-width: 400px;
    margin: 0 auto;
}

/* Progress Indicator */
.auth-progress {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 32px;
    padding: 0 20px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
    transform: scale(1.1);
}

.step-indicator {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--mobile-glass-bg);
    border: 2px solid var(--mobile-glass-border);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.progress-step.active .step-indicator {
    background: white;
    color: var(--mobile-primary);
    border-color: white;
    box-shadow: var(--mobile-shadow-light);
}

.progress-step span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.progress-step.active span {
    color: white;
    font-weight: 600;
}

/* Mobile Auth Header */
.mobile-auth-header {
    text-align: center;
    margin-bottom: 32px;
    color: white;
}

.mobile-auth-header .auth-logo {
    margin-bottom: 20px;
}

.logo-animation {
    position: relative;
    display: inline-block;
    margin-bottom: 16px;
}

.logo-icon {
    font-size: 48px;
    position: relative;
    z-index: 2;
    animation: logoFloat 3s ease-in-out infinite;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

@keyframes logoGlow {
    0% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

.mobile-auth-header h1 {
    font-size: 28px;
    font-weight: 800;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
}

.mobile-auth-header .auth-subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
    margin: 0;
    line-height: 1.4;
}

/* Mobile Tab Navigation */
.mobile-auth-tabs {
    display: flex;
    background: var(--mobile-glass-bg);
    border-radius: var(--mobile-border-radius);
    padding: 6px;
    margin-bottom: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid var(--mobile-glass-border);
    box-shadow: var(--mobile-shadow-light);
}

.mobile-tab-btn {
    flex: 1;
    padding: 16px 20px;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-size: 15px;
    position: relative;
    overflow: hidden;
    min-height: var(--touch-target);
}

.mobile-tab-btn .tab-icon {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.mobile-tab-btn.active {
    background: white;
    color: var(--mobile-primary);
    box-shadow: var(--mobile-shadow-medium);
    transform: translateY(-2px);
}

.mobile-tab-btn.active .tab-icon {
    transform: scale(1.1);
}

.mobile-tab-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--mobile-primary);
    border-radius: 2px;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.mobile-tab-btn.active .tab-indicator {
    transform: scaleX(1);
}

/* Mobile Auth Forms */
.mobile-auth-form {
    background: var(--mobile-card-gradient);
    border-radius: var(--mobile-border-radius);
    padding: 32px 24px;
    box-shadow: var(--mobile-shadow-strong);
    border: 1px solid var(--mobile-glass-border);
    backdrop-filter: blur(20px);
    display: none;
    animation: slideInUp 0.4s ease;
}

.mobile-auth-form.active {
    display: block;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Mobile Form Header */
.mobile-form-header {
    text-align: center;
    margin-bottom: 28px;
}

.mobile-form-header h2 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #2d3748;
    letter-spacing: -0.3px;
}

.mobile-form-header p {
    font-size: 16px;
    color: #718096;
    margin: 0;
    line-height: 1.4;
}

/* Mobile Form Groups */
.mobile-form-group {
    margin-bottom: 24px;
}

.mobile-label {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.label-required {
    color: var(--mobile-danger);
    font-weight: 700;
}

.label-optional {
    color: #a0aec0;
    font-weight: 400;
    font-size: 13px;
}

/* Mobile Input Wrapper */
.mobile-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.mobile-input-wrapper .input-icon {
    position: absolute;
    left: 16px;
    z-index: 2;
    color: #a0aec0;
    transition: color 0.3s ease;
}

.mobile-input {
    width: 100%;
    height: var(--mobile-input-height);
    padding: 0 16px 0 52px;
    border: 2px solid #e2e8f0;
    border-radius: 14px;
    font-size: var(--mobile-font-size);
    font-weight: 500;
    background: #f7fafc;
    color: #2d3748;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
}

.mobile-input:focus {
    outline: none;
    border-color: var(--mobile-primary);
    background: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.mobile-input:focus + .input-status,
.mobile-input:focus ~ .mobile-password-toggle {
    color: var(--mobile-primary);
}

.mobile-input::placeholder {
    color: #a0aec0;
    font-size: 15px;
}

/* Input Status Indicator */
.input-status {
    position: absolute;
    right: 16px;
    z-index: 2;
    opacity: 0;
    transition: all 0.3s ease;
}

.input-status.valid {
    opacity: 1;
    color: var(--mobile-accent);
}

.input-status.invalid {
    opacity: 1;
    color: var(--mobile-danger);
}

/* Mobile Password Toggle */
.mobile-password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    color: #a0aec0;
    transition: all 0.3s ease;
    min-width: var(--touch-target);
    min-height: var(--touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.mobile-password-toggle:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--mobile-primary);
}

/* Mobile Field Errors */
.mobile-field-error {
    color: var(--mobile-danger);
    font-size: 14px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(229, 62, 62, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(229, 62, 62, 0.2);
    display: none;
    align-items: center;
    gap: 8px;
    line-height: 1.3;
    animation: errorSlideIn 0.3s ease;
}

.mobile-field-error:not(:empty) {
    display: flex;
}

.mobile-field-error::before {
    content: '⚠️';
    font-size: 14px;
    flex-shrink: 0;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Field Hints */
.mobile-field-hint {
    font-size: 13px;
    color: #718096;
    margin-top: 6px;
    padding: 6px 12px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    line-height: 1.3;
}

.mobile-field-hint svg {
    flex-shrink: 0;
    opacity: 0.7;
}

/* Mobile Password Strength */
.mobile-password-strength {
    margin-top: 12px;
}

.strength-bar {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.mobile-password-strength.weak .strength-fill {
    width: 33%;
    background: linear-gradient(90deg, var(--mobile-danger) 0%, #fc8181 100%);
}

.mobile-password-strength.medium .strength-fill {
    width: 66%;
    background: linear-gradient(90deg, var(--mobile-warning) 0%, #fbb040 100%);
}

.mobile-password-strength.strong .strength-fill {
    width: 100%;
    background: linear-gradient(90deg, var(--mobile-accent) 0%, #68d391 100%);
}

.strength-text {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
}

/* Mobile Checkboxes */
.mobile-checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 15px;
    line-height: 1.5;
    color: #4a5568;
}

.mobile-checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.mobile-checkmark {
    width: 24px;
    height: 24px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    background: #f7fafc;
    transition: all 0.3s ease;
    flex-shrink: 0;
    position: relative;
    margin-top: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-checkbox-wrapper input[type="checkbox"]:checked + .mobile-checkmark {
    background: var(--mobile-primary);
    border-color: var(--mobile-primary);
    transform: scale(1.05);
}

.mobile-checkbox-wrapper input[type="checkbox"]:checked + .mobile-checkmark svg {
    opacity: 1;
    transform: scale(1);
    color: white;
}

.mobile-checkmark svg {
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.2s ease;
}

.checkbox-text {
    font-weight: 500;
}

/* Mobile Form Options */
.mobile-form-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 28px;
}

.mobile-forgot-link {
    background: none;
    border: none;
    color: var(--mobile-primary);
    font-weight: 600;
    font-size: 15px;
    text-align: center;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: var(--touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-forgot-link:hover {
    background: rgba(102, 126, 234, 0.1);
    text-decoration: none;
}

/* Mobile Buttons */
.mobile-btn {
    width: 100%;
    height: var(--mobile-button-height);
    border: none;
    border-radius: 14px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: none;
    letter-spacing: 0.3px;
    margin-bottom: 16px;
}

.mobile-btn-primary {
    background: var(--mobile-bg-gradient);
    color: white;
    box-shadow: var(--mobile-shadow-medium);
}

.mobile-btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--mobile-shadow-strong);
}

.mobile-btn-secondary {
    background: linear-gradient(135deg, var(--mobile-accent) 0%, #48bb78 100%);
    color: white;
    box-shadow: var(--mobile-shadow-light);
}

.mobile-btn-outline {
    background: transparent;
    color: var(--mobile-primary);
    border: 2px solid var(--mobile-primary);
    box-shadow: none;
}

.mobile-btn-outline:hover {
    background: var(--mobile-primary);
    color: white;
}

.mobile-btn:active {
    transform: translateY(0);
}

.mobile-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Button Content */
.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    z-index: 2;
}

.btn-text {
    transition: opacity 0.3s ease;
}

.btn-loader {
    display: none;
}

.mobile-btn.loading .btn-text {
    opacity: 0.7;
}

.mobile-btn.loading .btn-loader {
    display: block;
}

.loader-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button Ripple Effect */
.btn-ripple {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    overflow: hidden;
    pointer-events: none;
}

.btn-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.mobile-btn:active .btn-ripple::before {
    width: 300px;
    height: 300px;
}

/* Mobile Registration Steps */
.mobile-registration-steps {
    margin-bottom: 28px;
}

.step-indicator-bar {
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    margin-bottom: 12px;
    overflow: hidden;
}

.step-progress {
    height: 100%;
    background: var(--mobile-bg-gradient);
    border-radius: 2px;
    width: 33%;
    transition: width 0.4s ease;
}

.step-labels {
    display: flex;
    justify-content: space-between;
}

.step-label {
    font-size: 12px;
    color: #a0aec0;
    font-weight: 500;
    transition: color 0.3s ease;
}

.step-label.active {
    color: var(--mobile-primary);
    font-weight: 600;
}

/* Registration Step Content */
.registration-step {
    display: none;
}

.registration-step.active {
    display: block;
    animation: stepSlideIn 0.4s ease;
}

@keyframes stepSlideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.mobile-step-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.mobile-step-actions .mobile-btn {
    margin-bottom: 0;
}

/* Mobile PIN Input */
.mobile-pin-input {
    text-align: center;
    letter-spacing: 8px;
    font-size: 18px;
    font-weight: 600;
}

/* Mobile Terms Section */
.mobile-terms-section {
    margin-bottom: 24px;
}

.terms-header {
    text-align: center;
    margin-bottom: 20px;
}

.terms-header h3 {
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 8px 0;
    color: #2d3748;
}

.terms-header p {
    font-size: 14px;
    color: #718096;
    margin: 0;
}

.mobile-terms-content {
    margin-bottom: 24px;
}

.terms-summary {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.terms-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.terms-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.terms-text h4 {
    font-size: 15px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #2d3748;
}

.terms-text p {
    font-size: 13px;
    color: #718096;
    margin: 0;
    line-height: 1.3;
}

.mobile-terms-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 20px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-terms-checkbox:hover {
    background: rgba(102, 126, 234, 0.08);
    border-color: rgba(102, 126, 234, 0.2);
}

.mobile-terms-checkbox input[type="checkbox"] {
    display: none;
}

.mobile-terms-checkbox .mobile-checkmark {
    margin-top: 0;
}

.mobile-terms-checkbox .terms-text {
    font-size: 14px;
    line-height: 1.5;
    color: #4a5568;
}

.terms-link {
    color: var(--mobile-primary);
    font-weight: 600;
    text-decoration: underline;
    transition: color 0.3s ease;
}

.terms-link:hover {
    color: var(--mobile-primary-dark);
}

/* Mobile Reset Steps */
.mobile-reset-steps {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 28px;
    padding: 0 20px;
}

.reset-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.reset-step.active {
    opacity: 1;
    transform: scale(1.05);
}

.reset-step .step-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    background: var(--mobile-glass-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--mobile-glass-border);
    transition: all 0.3s ease;
}

.reset-step.active .step-icon {
    background: white;
    border-color: white;
    box-shadow: var(--mobile-shadow-light);
}

.reset-step span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.reset-step.active span {
    color: white;
    font-weight: 600;
}

/* Mobile Security Notice */
.mobile-security-notice {
    background: rgba(56, 161, 105, 0.1);
    border: 1px solid rgba(56, 161, 105, 0.2);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.mobile-security-notice .security-icon {
    font-size: 24px;
    flex-shrink: 0;
    margin-top: 2px;
}

.mobile-security-notice .security-content h4 {
    font-size: 15px;
    font-weight: 600;
    margin: 0 0 6px 0;
    color: #2d3748;
}

.mobile-security-notice .security-content p {
    font-size: 13px;
    color: #718096;
    margin: 0;
    line-height: 1.4;
}

/* Mobile Alternative Actions */
.mobile-alternative-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    gap: 16px;
}

.mobile-link-btn {
    background: none;
    border: none;
    color: var(--mobile-primary);
    font-weight: 600;
    font-size: 15px;
    padding: 12px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    min-height: var(--touch-target);
}

.mobile-link-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    text-decoration: none;
}

/* Mobile Form Footer */
.mobile-form-footer {
    text-align: center;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.mobile-form-footer p {
    color: #718096;
    font-size: 15px;
    margin: 0 0 12px 0;
    font-weight: 500;
}

/* Mobile Message */
.mobile-message {
    width: 100%;
    max-width: 400px;
    margin: 20px auto 0;
    padding: 16px 20px;
    border-radius: 12px;
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 12px;
    animation: messageSlideIn 0.4s ease;
    line-height: 1.4;
    box-shadow: var(--mobile-shadow-light);
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.mobile-message.success {
    background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.mobile-message.success::before {
    content: '✅';
    font-size: 18px;
}

.mobile-message.error {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    color: #742a2a;
    border: 1px solid #feb2b2;
}

.mobile-message.error::before {
    content: '❌';
    font-size: 18px;
}

.mobile-message.info {
    background: linear-gradient(135deg, #bee3f8 0%, #90cdf4 100%);
    color: #2a4365;
    border: 1px solid #90cdf4;
}

.mobile-message.info::before {
    content: 'ℹ️';
    font-size: 18px;
}

/* Mobile Security Notice Footer */
.mobile-security-notice-footer {
    margin-top: 32px;
    text-align: center;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.security-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: var(--mobile-glass-bg);
    border-radius: 12px;
    border: 1px solid var(--mobile-glass-border);
    backdrop-filter: blur(10px);
    min-width: 80px;
}

.badge-icon {
    font-size: 20px;
}

.badge-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.badge-title {
    font-size: 12px;
    font-weight: 600;
    color: white;
    line-height: 1;
}

.badge-subtitle {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1;
}

.security-footer-text {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    line-height: 1.4;
    max-width: 300px;
    margin: 0 auto;
}

/* Mobile Bottom Helper */
.mobile-bottom-helper {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    opacity: 0.7;
    animation: helperPulse 2s ease-in-out infinite;
}

.helper-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--mobile-glass-bg);
    border-radius: 20px;
    border: 1px solid var(--mobile-glass-border);
    backdrop-filter: blur(10px);
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    font-weight: 500;
}

.helper-icon {
    font-size: 14px;
}

@keyframes helperPulse {
    0%, 100% { opacity: 0.7; transform: translateX(-50%) scale(1); }
    50% { opacity: 1; transform: translateX(-50%) scale(1.05); }
}

/* Responsive Design */
@media (max-width: 480px) {
    :root {
        --mobile-padding: 16px;
        --mobile-gap: 12px;
        --mobile-border-radius: 12px;
        --mobile-input-height: 52px;
        --mobile-button-height: 48px;
    }

    .mobile-auth-container {
        padding: 70px var(--mobile-padding) var(--mobile-padding);
    }

    .mobile-auth-header h1 {
        font-size: 24px;
    }

    .mobile-auth-header .auth-subtitle {
        font-size: 15px;
    }

    .logo-icon {
        font-size: 40px;
    }

    .mobile-auth-form {
        padding: 24px 20px;
    }

    .mobile-form-header h2 {
        font-size: 20px;
    }

    .mobile-tab-btn {
        padding: 14px 16px;
        font-size: 14px;
    }

    .mobile-tab-btn .tab-icon {
        font-size: 18px;
    }

    .auth-progress {
        gap: 16px;
    }

    .step-indicator {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .security-badges {
        gap: 12px;
    }

    .security-badge {
        min-width: 70px;
        padding: 10px;
    }

    .mobile-alternative-actions {
        flex-direction: column;
        gap: 12px;
    }

    .mobile-link-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 360px) {
    .mobile-auth-container {
        padding: 60px 12px 12px;
    }

    .mobile-auth-form {
        padding: 20px 16px;
    }

    .mobile-form-group {
        margin-bottom: 20px;
    }

    .mobile-auth-header h1 {
        font-size: 22px;
    }

    .logo-icon {
        font-size: 36px;
    }

    .mobile-tab-btn {
        padding: 12px 12px;
        font-size: 13px;
    }

    .mobile-tab-btn .tab-icon {
        display: none;
    }

    .mobile-input-height: 48px;
    .mobile-button-height: 44px;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .mobile-auth-form.active {
        animation: none;
    }

    .mobile-btn:hover {
        transform: none;
    }

    .mobile-message {
        animation: none;
    }

    .logo-icon,
    .logo-glow {
        animation: none;
    }

    .mobile-bottom-helper {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .mobile-input {
        border-width: 3px;
    }

    .mobile-btn {
        border: 2px solid currentColor;
    }

    .mobile-message {
        border-width: 2px;
    }

    .mobile-checkmark {
        border-width: 3px;
    }
}

/* Print Styles */
@media print {
    .mobile-nav,
    .mobile-bottom-helper,
    .mobile-security-notice-footer {
        display: none;
    }

    .mobile-auth-container {
        background: white;
        padding: 20px;
    }

    .mobile-auth-form {
        background: white;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
