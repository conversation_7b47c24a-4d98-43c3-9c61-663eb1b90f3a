<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test - TLS Wallet</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/public.css">
    <link rel="stylesheet" href="css/faq.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 8px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
        }
        .test-title {
            color: var(--text-primary);
            margin-bottom: 15px;
        }
        .test-text {
            color: var(--text-secondary);
            margin-bottom: 15px;
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">Toggle Theme</button>
    
    <div class="test-container">
        <h1 style="color: var(--text-primary); text-align: center;">Dark Mode Test</h1>
        
        <div class="test-section">
            <h2 class="test-title">Background Colors</h2>
            <p class="test-text">This card uses --bg-secondary background</p>
            <p class="test-text">Body uses --bg-primary background</p>
            <div style="background: var(--bg-tertiary); padding: 10px; border-radius: 4px; margin-top: 10px;">
                <p class="test-text">This uses --bg-tertiary background</p>
            </div>
        </div>

        <div class="test-section">
            <h2 class="test-title">Text Colors</h2>
            <p style="color: var(--text-primary);">Primary text color</p>
            <p style="color: var(--text-secondary);">Secondary text color</p>
            <p style="color: var(--text-muted);">Muted text color</p>
        </div>

        <div class="test-section">
            <h2 class="test-title">Buttons</h2>
            <button class="btn btn-primary">Primary Button</button>
            <button class="btn btn-secondary">Secondary Button</button>
            <button class="btn btn-success">Success Button</button>
            <button class="btn btn-danger">Danger Button</button>
            <button class="btn btn-outline">Outline Button</button>
        </div>

        <div class="test-section">
            <h2 class="test-title">Messages</h2>
            <div class="message success" style="display: block;">Success message</div>
            <div class="message error" style="display: block;">Error message</div>
            <div class="message info" style="display: block;">Info message</div>
            <div class="message warning" style="display: block;">Warning message</div>
        </div>

        <div class="test-section">
            <h2 class="test-title">Cards</h2>
            <div class="card">
                <h3>Card Title</h3>
                <p>This is a card with variable-based styling that adapts to dark mode.</p>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            // This simulates dark mode by adding/removing a class
            // In real usage, dark mode is detected automatically via prefers-color-scheme
            const isDark = document.body.classList.contains('force-dark');
            
            if (isDark) {
                document.body.classList.remove('force-dark');
                document.body.style.filter = '';
            } else {
                document.body.classList.add('force-dark');
                // Apply a dark filter to simulate dark mode for testing
                document.body.style.filter = 'invert(1) hue-rotate(180deg)';
            }
        }
    </script>
</body>
</html>
