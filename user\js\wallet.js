// Wallet Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    initWalletPage();
});

let walletData = null;
let withdrawalConfig = null; // Store withdrawal configuration

// Helper function to safely toggle button visibility
function toggleButton(buttonId, show) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.style.display = show ? 'inline-block' : 'none';
    }
}

function initWalletPage() {
    setupWalletForms();
    loadWalletData();
    loadWithdrawalConfig();
    initFooterMenu();
}

function setupWalletForms() {
    // Remove wallet creation functionality - wallets are created per deposit
    // Only keep withdrawal functionality
    const withdrawForm = document.getElementById('withdrawForm');

    if (withdrawForm) {
        withdrawForm.addEventListener('submit', handleWithdraw);
        
        // Add real-time validation to amount field
        const amountField = withdrawForm.querySelector('input[name="amount"]');
        if (amountField) {
            amountField.addEventListener('input', validateWithdrawalAmount);
            amountField.addEventListener('blur', validateWithdrawalAmount);
        }
    }
}

/**
 * Load withdrawal configuration from backend
 */
async function loadWithdrawalConfig() {
    try {
        const response = await apiCall('get_withdrawal_config');
        if (response.success) {
            withdrawalConfig = response.config;
            console.log('Withdrawal config loaded:', withdrawalConfig);
            
            // Update placeholder text with minimum amount
            const amountField = document.querySelector('input[name="amount"]');
            if (amountField && withdrawalConfig.min_amount) {
                amountField.placeholder = `Minimum: ${withdrawalConfig.min_amount} USDT`;
                amountField.min = withdrawalConfig.min_amount;
            }
            
            // Update instructions text
            const minAmountText = document.getElementById('minAmountText');
            if (minAmountText && withdrawalConfig.min_amount) {
                minAmountText.textContent = `The minimum withdrawal amount is ${withdrawalConfig.min_amount} USDT`;
            }
        }
    } catch (error) {
        console.error('Error loading withdrawal config:', error);
    }
}

/**
 * Validate withdrawal amount in real-time
 */
function validateWithdrawalAmount(event) {
    const amountField = event.target;
    const amount = parseFloat(amountField.value);
    
    // Remove any existing validation styling
    amountField.classList.remove('invalid', 'valid');
    
    if (amountField.value && withdrawalConfig) {
        if (amount < withdrawalConfig.min_amount) {
            amountField.classList.add('invalid');
            amountField.title = `Minimum withdrawal amount is ${withdrawalConfig.min_amount} USDT`;
        } else if (walletData && walletData.balance) {
            const currentBalance = parseFloat(walletData.balance);
            if (amount > currentBalance) {
                amountField.classList.add('invalid');
                amountField.title = `Insufficient balance. Available: ${currentBalance.toFixed(2)} USDT`;
            } else if (amount > 0) {
                amountField.classList.add('valid');
                amountField.title = '';
            }
        } else if (amount > 0) {
            amountField.classList.add('valid');
            amountField.title = '';
        }
    }
}

async function loadWalletData() {
    console.log('Loading wallet data...');
    
    try {
        const balanceElement = document.getElementById('walletBalance');
        if (balanceElement) {
            balanceElement.value = 'Loading... USDT';
            
            // Ensure balance is loaded completely before continuing
            await fetchLiveBalance();
            
            // Wait a bit more and check if balance was set correctly
            await new Promise(resolve => setTimeout(resolve, 500));
            
            if (!walletData || !walletData.balance || walletData.balance === '0.00') {
                console.warn('Balance still not loaded correctly, trying again...');
                await fetchLiveBalance();
            }
            
            console.log('Final walletData after loading:', walletData);
        }
        
        // Hide wallet creation button, show refresh button
        toggleButton('createWalletBtn', false);
        toggleButton('refreshBalanceBtn', true);
        toggleButton('copyAddressBtn', false);
        
    } catch (error) {
        console.error('Error loading wallet data:', error);
        showMessage('Error loading wallet data', 'error');
    }
}

function updateWalletDisplay() {
    if (walletData) {
        const addressElement = document.getElementById('walletAddress');
        const balanceElement = document.getElementById('walletBalance');
        
        if (addressElement) addressElement.value = walletData.address;        
        
        if (balanceElement) {
            // Show loading while fetching actual balance
            balanceElement.value = 'Loading... USDT';
            
            // Fetch actual withdrawable balance
            fetchLiveBalance();
        }
          // Show/hide buttons based on wallet existence
        toggleButton('createWalletBtn', false);
        toggleButton('refreshBalanceBtn', true);
        toggleButton('copyAddressBtn', true);
    }
}

/**
 * Fetch and display actual withdrawable balance (total earned minus total withdrawals)
 */
async function fetchLiveBalance(forceRefresh = false) {
    try {
        // Add cache busting parameter if force refresh is requested
        const endpoint = forceRefresh ? 'get_wallet_balance' : 'get_wallet_balance';
        const requestData = forceRefresh ? { _t: Date.now() } : null;
        
        const response = await apiCall(endpoint, requestData);
        
        const balanceElement = document.getElementById('walletBalance');
        const accountBalance = document.getElementById('accountBalance');
        const referalBonus = document.getElementById('referalBonus');

        if (!balanceElement) {
            console.error('Balance element not found');
            return false;
        }
          if (response.success && response.balance !== undefined && response.balance !== null) {
            // Show actual withdrawable balance (total earned - total withdrawals)
            const balance = parseFloat(response.balance || 0).toFixed(2);
            const investmentEarning = parseFloat(response.investment_earning || 0).toFixed(2);
            const referralEarning = parseFloat(response.referral_bonus || 0).toFixed(2);
            
            balanceElement.value = `Total Balance: ${balance} USDT`;
            accountBalance.value = `Referral Earning: ${investmentEarning} USDT`
            referalBonus.value = `Total Earning: ${referralEarning} USDT`
              // Store balance data for withdrawal validation
            if (!walletData) walletData = {};
            const oldBalance = walletData.balance;
            walletData.balance = balance;
            
            // Log balance change for debugging
            if (forceRefresh && oldBalance !== balance) {
                console.log(`Balance updated: ${oldBalance} → ${balance} USDT`);
            }
            
            // Show balance breakdown if available for debugging in development
            if (response.debug && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
                console.log('Balance breakdown:', response.debug);
            }
            
            return true;        } else {
            // Handle API errors - show more detailed error information
            balanceElement.value = '0.00 USDT';
            
            // Reset wallet data
            if (!walletData) walletData = {};
            walletData.balance = '0.00';
            
            // Show user-friendly error message with more details
            if (response.error && response.error.includes('authenticated')) {
                showMessage('Please log in to view your balance', 'info');
            } else if (response.message) {
                showMessage(`Balance error: ${response.message}`, 'error');
            } else if (response.error) {
                showMessage(`Unable to load balance: ${response.error}`, 'error');
            } else {
                showMessage('Unable to load balance data', 'error');
            }
            
            return false;        }
    } catch (error) {
        console.error('Error fetching balance:', error);
        const balanceElement = document.getElementById('walletBalance');
        if (balanceElement) {
            balanceElement.value = 'Error loading';
        }
        
        // Reset wallet data
        if (!walletData) walletData = {};
        walletData.balance = '0.00';
        
        showMessage('Network error. Please check your connection.', 'error');
        return false;
    }
}

/**
 * Refresh live balance with loading indication
 */
async function refreshLiveBalance() {
    const btn = document.getElementById('refreshBalanceBtn');
    if (btn) {
        setButtonLoading(btn, true);
    }
      try {
        console.log('Manual balance refresh triggered');
        await fetchLiveBalance(true); // Force refresh with cache busting
        showMessage('Balance refreshed successfully!', 'success');
    } catch (error) {
        console.error('Error refreshing balance:', error);
        showMessage('Error refreshing balance. Please try again.', 'error');
    } finally {
        if (btn) {
            setButtonLoading(btn, false);
        }
    }
}

// Remove the createWallet function since wallets are now created per deposit
// Users no longer manually create wallets

async function handleWithdraw(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    const to = formData.get('address');
    const amount = parseFloat(formData.get('amount'));
    const pin = formData.get('pin');
    
    // Validate PIN
    if (!pin || !/^[0-9]{5}$/.test(pin)) {
        showWithdrawalModal({
            success: false,
            error: 'Please enter your 5-digit PIN to withdraw.',
            title: 'PIN Required'
        });
        return;
    }
    
    if (!to || !amount) {
        showWithdrawalModal({
            success: false,
            error: 'Please fill in all fields',
            title: 'Missing Information'
        });
        return;
    }
    
    if (amount <= 0) {
        showWithdrawalModal({
            success: false,
            error: 'Amount must be greater than 0',
            title: 'Invalid Amount'
        });
        return;
    }
    
    // Client-side minimum amount validation
    if (withdrawalConfig && amount < withdrawalConfig.min_amount) {
        showWithdrawalModal({
            success: false,
            error: `Minimum withdrawal amount is ${withdrawalConfig.min_amount} USDT`,
            title: 'Minimum Amount Required',
            isMinimumError: true
        });
        return;
    }    // Check if wallet has sufficient balance for withdrawal
    // Force refresh balance if not available
    if (!walletData || !walletData.balance || walletData.balance === '0.00') {
        await fetchLiveBalance();
    }
    
    // Double-check balance availability and parse correctly
    const currentBalance = walletData?.balance ? parseFloat(walletData.balance) : 0;
    
    if (currentBalance > 0 && amount > currentBalance) {
        showWithdrawalModal({
            success: false,
            error: `Insufficient balance. Available: ${currentBalance.toFixed(2)} USDT`,
            title: 'Insufficient Funds'
        });
        return;
    }    
    console.log('Balance check passed, proceeding with withdrawal...');
    setButtonLoading(submitBtn, true);
      try {        
        const response = await apiCall('withdraw', {
            address: to, // must be 'address' for backend
            amount: amount,
            pin: pin
        });        
        // Show withdrawal result modal with simple success/error messages
        showWithdrawalModal(response);
        
        if (response.success) {
            form.reset();
            
            // Store the current balance before withdrawal for comparison
            const currentBalance = walletData?.balance || '0.00';
            console.log('Balance before withdrawal:', currentBalance);
            
            // Add delay before refreshing balance to allow backend processing
            setTimeout(async () => {
                console.log('Refreshing balance after successful withdrawal...');
                await loadWalletData();
                
                // Check if balance has changed
                const newBalance = walletData?.balance || '0.00';
                console.log('Balance after first refresh:', newBalance);
                
                // If balance hasn't changed, try force refresh with longer delay
                if (currentBalance === newBalance) {
                    console.log('Balance unchanged, force refreshing in 2 seconds...');
                    setTimeout(async () => {
                        console.log('Force refreshing balance...');
                        await fetchLiveBalance(true);
                        
                        // Final check
                        const finalBalance = walletData?.balance || '0.00';
                        console.log('Final balance after force refresh:', finalBalance);
                        
                        if (currentBalance === finalBalance) {
                            console.warn('Balance still unchanged after withdrawal. This may indicate a backend processing delay.');
                            showMessage('Withdrawal processed. Balance may take a moment to update.', 'info');
                        }
                    }, 2000);
                }
            }, 1000);
        }
    } catch (error) {
        console.error('Error processing withdrawal:', error);
        // Show simple error modal
        showWithdrawalModal({
            success: false,
            error: 'Network error: ' + error.message,
            title: 'Connection Error'
        });
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

function copyToClipboard() {
    if (walletData && walletData.address) {
        navigator.clipboard.writeText(walletData.address).then(() => {
            showMessage('Address copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = walletData.address;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('Address copied to clipboard!', 'success');
        });
    }
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    // Define state-changing operations that require CSRF protection
    const stateChangingActions = [
        'create_investment', 'withdraw', 'record_deposit', 'change_password'
    ];
    
    // For state-changing operations, get CSRF token
    if (stateChangingActions.includes(endpoint)) {
        try {
            // Get CSRF token
            const csrfResponse = await fetch('../ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_csrf_token',
                    context: 'user'
                })
            });
            
            if (!csrfResponse.ok) {
                throw new Error(`Failed to get CSRF token: ${csrfResponse.status}`);
            }
            
            const csrfResult = await csrfResponse.json();
            if (!csrfResult.success || !csrfResult.csrf_token) {
                throw new Error('Failed to obtain CSRF token');
            }
            
            // Make the actual request with CSRF token
            const url = `../ajax.php`;
            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: endpoint,
                    csrf_token: csrfResult.csrf_token,
                    ...data
                })
            };
            
            const response = await fetch(url, options);
            return await response.json();
            
        } catch (error) {
            console.error('CSRF protected API call failed:', error);
            showMessage('Security token error. Please refresh the page and try again.', 'error');
            throw error;
        }
    }
    
    // Regular API call for read-only operations
    const url = `../ajax.php`;
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...data
        })
    };
    
    const response = await fetch(url, options);
    return await response.json();
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    if (messageDiv) {
        messageDiv.textContent = message;
        messageDiv.className = `message ${securityUtils.escapeHtml(type)}`;
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 3000);
    } else {
        // Fallback to console if no message element
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

function setButtonLoading(button, loading) {
    if (!button) return;
    
    if (loading) {
        button.disabled = true;
        button.dataset.originalText = button.textContent;
        button.textContent = 'Loading...';
    } else {
        button.disabled = false;
        button.textContent = button.dataset.originalText || button.textContent;
    }
}

function initFooterMenu() {
    // Add smooth entrance animation for footer menu
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Always show footer menu
        footerMenu.classList.add('show');
        
        // Optional: Handle window resize for future mobile-specific behavior
        window.addEventListener('resize', function() {
            // Footer is always visible now, but this can be customized later
            footerMenu.classList.add('show');
        });
    }
}

// Enhanced Withdrawal Modal Functions
function showWithdrawalModal(response) {
    const modal = document.getElementById('withdrawalModal');
    
    if (!modal) {
        console.error('Withdrawal modal not found');
        return;
    }
    
    // Reset modal state
    resetModalState();
    
    // Configure modal content
    if (response.success) {
        setupSuccessModal(response);
    } else {
        setupErrorModal(response);
    }
    
    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // Setup close handlers
    setupModalCloseHandlers();
    
    // Focus management
    setTimeout(() => {
        if (response.success) {
            document.getElementById('continueBtn')?.focus();
        } else {
            document.getElementById('okModalBtn')?.focus();
        }
    }, 500);
}

function resetModalState() {
    // Hide all conditional sections
    const successDetails = document.getElementById('successDetails');
    const errorDetails = document.getElementById('errorDetails');
    const successActions = document.getElementById('successActions');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    
    if (successDetails) {
        successDetails.classList.add('hidden');
        successDetails.classList.remove('visible');
    }
    if (errorDetails) {
        errorDetails.style.display = 'none';
    }
    if (successActions) {
        successActions.style.display = 'none';
    }
    if (tryAgainBtn) {
        tryAgainBtn.style.display = 'none';
    }
    
    // Reset modal icon
    const modalIcon = document.getElementById('modalIcon');
    if (modalIcon) {
        modalIcon.className = 'modal-icon';
    }
    
    // Reset detail row styling
    const detailRows = document.querySelectorAll('.detail-row');
    detailRows.forEach(row => {
        row.classList.remove('amount-row', 'fee-row', 'received-row');
    });
}

function setupSuccessModal(response) {
    const modalIcon = document.getElementById('modalIcon');
    const modalIconSymbol = document.getElementById('modalIconSymbol');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');
    const successDetails = document.getElementById('successDetails');
    const successActions = document.getElementById('successActions');
    
    // Configure success icon and header
    modalIcon.classList.add('success');
    modalIconSymbol.textContent = '✓';
    modalTitle.textContent = 'Withdrawal Successful!';
    modalMessage.textContent = 'Withdrawal sent successfully. Your funds will be processed shortly.';
    
    // Hide details section - only show basic success message
    successDetails.classList.add('hidden');
    successDetails.classList.remove('visible');
    
    // Show success actions (just the OK/Continue button)
    successActions.style.display = 'flex';
}

function setupErrorModal(response) {
    const modalIcon = document.getElementById('modalIcon');
    const modalIconSymbol = document.getElementById('modalIconSymbol');
    const modalTitle = document.getElementById('modalTitle');
    const modalMessage = document.getElementById('modalMessage');
    const errorDetails = document.getElementById('errorDetails');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    
    // Configure error icon and header
    modalIcon.classList.add('error');
    modalIconSymbol.textContent = '✗';
    modalTitle.textContent = 'Withdrawal Failed';
    modalMessage.textContent = 'Error encountered while processing withdrawal, please try again.';
    
    // Hide error details section - only show basic error message
    if (errorDetails) {
        errorDetails.style.display = 'none';
    }
    
    // Show try again button
    if (tryAgainBtn) {
        tryAgainBtn.style.display = 'inline-block';
    }
}

function copyToClipboard() {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showCopyFeedback(successMessage);
    } catch (err) {
        console.error('Failed to copy to clipboard:', err);
        showCopyFeedback('Failed to copy to clipboard');
    }
    
    document.body.removeChild(textArea);
}

function showCopyFeedback(message) {
    // Create temporary feedback element
    const feedback = document.createElement('div');
    feedback.textContent = message;
    feedback.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #28a745;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 0.9rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        animation: fadeInOut 2s ease-in-out forwards;
    `;
    
    // Add animation keyframes if not already present
    if (!document.querySelector('#copyFeedbackStyles')) {
        const style = document.createElement('style');
        style.id = 'copyFeedbackStyles';
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(feedback);
    
    setTimeout(() => {
        if (feedback.parentNode) {
            feedback.parentNode.removeChild(feedback);
        }
    }, 2000);
}

function setupModalCloseHandlers() {
    const modal = document.getElementById('withdrawalModal');
    const closeBtn = document.getElementById('closeModal');
    const okBtn = document.getElementById('okModalBtn');
    const continueBtn = document.getElementById('continueBtn');
    const tryAgainBtn = document.getElementById('tryAgainBtn');
    
    // Close button handler
    if (closeBtn) {
        closeBtn.onclick = hideWithdrawalModal;
    }
    
    // OK button handler
    if (okBtn) {
        okBtn.onclick = hideWithdrawalModal;
    }
    
    // Continue button handler (success modal)
    if (continueBtn) {
        continueBtn.onclick = () => {
            hideWithdrawalModal();
            // Refresh balance to show updated amount
            refreshLiveBalance();
        };
    }
    
    // Try Again button handler (error modal)
    if (tryAgainBtn) {
        tryAgainBtn.onclick = () => {
            hideWithdrawalModal();
            // Focus on the relevant form field based on error type
            const modalTitle = document.getElementById('modalTitle').textContent;
            if (modalTitle.includes('Address')) {
                document.getElementById('withdrawAddress')?.focus();
            } else if (modalTitle.includes('Amount') || modalTitle.includes('Balance')) {
                document.getElementById('withdrawAmount')?.focus();
            }
        };
    }
    
    // Click outside to close
    modal.onclick = function(e) {
        if (e.target === modal) {
            hideWithdrawalModal();
        }
    };
    
    // Escape key to close
    document.addEventListener('keydown', function escapeHandler(e) {
        if (e.key === 'Escape') {
            hideWithdrawalModal();
            document.removeEventListener('keydown', escapeHandler);
        }
    });
}

function hideWithdrawalModal() {
    const modal = document.getElementById('withdrawalModal');
    if (modal) {
        // Add fade out animation
        modal.style.animation = 'fadeOut 0.2s ease-out';
        
        setTimeout(() => {
            modal.style.display = 'none';
            modal.style.animation = '';
            document.body.style.overflow = '';
        }, 200);
    }
    
    // Add fade out animation to CSS if not already present
    if (!document.querySelector('#modalFadeOutStyles')) {
        const style = document.createElement('style');
        style.id = 'modalFadeOutStyles';
        style.textContent = `
            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}




