// Enhanced Authentication JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initAuthPage();
});

function initAuthPage() {
    setupFormHandlers();
    setupValidation();
    setupPasswordToggles();
    setupTermsValidation();
    showLogin();
    
    // Verify and prefill referral code if present
    verifyAndPrefillReferral();
    
    // Setup accessibility
    setupAccessibility();
}

function setupFormHandlers() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }
}

function setupValidation() {
    // Real-time validation for all forms
    const inputs = document.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
        
        // Special handling for password fields
        if (input.type === 'password') {
            input.addEventListener('input', () => {
                if (input.id === 'registerPassword') {
                    updatePasswordStrength(input.value);
                }
                if (input.id === 'confirmPassword') {
                    validatePasswordMatch();
                }
            });
        }
        
        // Email validation
        if (input.type === 'email') {
            input.addEventListener('input', () => {
                if (input.value && !isValidEmail(input.value)) {
                    showFieldError(input, 'Please enter a valid email address');
                } else {
                    clearFieldError(input);
                }
            });
        }
        
        // PIN validation
        if (input.name === 'pin') {
            input.addEventListener('input', (e) => {
                // Only allow digits
                e.target.value = e.target.value.replace(/\D/g, '');
                
                if (e.target.value.length > 0 && e.target.value.length < 5) {
                    showFieldError(input, 'PIN must be exactly 5 digits');
                } else if (e.target.value.length === 5) {
                    clearFieldError(input);
                }
            });
        }
    });
    
    // Terms checkbox validation
    const termsCheckbox = document.getElementById('agreeTerms');
    if (termsCheckbox) {
        termsCheckbox.addEventListener('change', () => {
            if (!termsCheckbox.checked) {
                showFieldError(termsCheckbox, 'You must accept the Terms of Service and Privacy Policy to create an account');
            } else {
                clearFieldError(termsCheckbox);
            }
        });
    }
}

function setupPasswordToggles() {
    const toggles = document.querySelectorAll('.password-toggle');
    toggles.forEach(toggle => {
        toggle.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = toggle.getAttribute('onclick').match(/'([^']+)'/)[1];
            togglePassword(targetId);
        });
    });
}

function setupAccessibility() {
    // Add keyboard navigation for tabs
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach((tab, index) => {
        tab.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                e.preventDefault();
                const nextIndex = e.key === 'ArrowRight' ? 
                    (index + 1) % tabButtons.length : 
                    (index - 1 + tabButtons.length) % tabButtons.length;
                tabButtons[nextIndex].focus();
                tabButtons[nextIndex].click();
            }
        });
    });
}

function showLogin() {
    hideAllForms();
    document.getElementById('loginForm').classList.add('active');
    setActiveTab(0);
    document.getElementById('loginEmail').focus();
}

function showRegister() {
    hideAllForms();
    document.getElementById('registerForm').classList.add('active');
    setActiveTab(1);
    document.getElementById('registerEmail').focus();
}

function showForgotPassword() {
    hideAllForms();
    document.getElementById('forgotPasswordForm').classList.add('active');
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById('forgotEmail').focus();
}

function hideAllForms() {
    document.querySelectorAll('.auth-form').forEach(form => {
        form.classList.remove('active');
    });
}

function setActiveTab(index) {
    document.querySelectorAll('.tab-btn').forEach((btn, i) => {
        btn.classList.toggle('active', i === index);
        btn.setAttribute('aria-selected', i === index);
    });
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.parentElement.querySelector('.password-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        toggle.textContent = '🙈';
        toggle.setAttribute('aria-label', 'Hide password');
    } else {
        field.type = 'password';
        toggle.textContent = '👁️';
        toggle.setAttribute('aria-label', 'Show password');
    }
}

function validateField(field) {
    clearFieldError(field);
    
    if (!field.value.trim() && field.hasAttribute('required')) {
        showFieldError(field, `${getFieldLabel(field)} is required`);
        return false;
    }
    
    // Specific validations
    switch (field.type) {
        case 'email':
            if (field.value && !isValidEmail(field.value)) {
                showFieldError(field, 'Please enter a valid email address');
                return false;
            }
            break;
            
        case 'password':
            if (field.name === 'password' && field.value.length < 6) {
                showFieldError(field, 'Password must be at least 6 characters long');
                return false;
            }
            break;
    }
    
    // PIN validation
    if (field.name === 'pin') {
        if (field.value.length !== 5 || !/^\d{5}$/.test(field.value)) {
            showFieldError(field, 'PIN must be exactly 5 digits');
            return false;
        }
    }
    
    return true;
}

function validatePasswordMatch() {
    const password = document.getElementById('registerPassword');
    const confirmPassword = document.getElementById('confirmPassword');
    
    if (confirmPassword.value && password.value !== confirmPassword.value) {
        showFieldError(confirmPassword, 'Passwords do not match');
        return false;
    } else {
        clearFieldError(confirmPassword);
        return true;
    }
}

function updatePasswordStrength(password) {
    const strengthIndicator = document.getElementById('passwordStrength');
    if (!strengthIndicator) return;
    
    const strength = calculatePasswordStrength(password);
    
    strengthIndicator.className = `password-strength ${strength.level}`;
    strengthIndicator.setAttribute('aria-label', `Password strength: ${strength.level}`);
}

function calculatePasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    if (score < 3) return { level: 'weak', score };
    if (score < 5) return { level: 'medium', score };
    return { level: 'strong', score };
}

function showFieldError(field, message) {
    const errorId = field.id + 'Error';
    const errorElement = document.getElementById(errorId);
    
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'flex';
    }
    
    field.setAttribute('aria-invalid', 'true');
    field.setAttribute('aria-describedby', errorId);
    field.classList.add('error');
}

function clearFieldError(field) {
    const errorId = field.id + 'Error';
    const errorElement = document.getElementById(errorId);
    
    if (errorElement) {
        errorElement.textContent = '';
        errorElement.style.display = 'none';
    }
    
    field.removeAttribute('aria-invalid');
    field.removeAttribute('aria-describedby');
    field.classList.remove('error');
}

function getFieldLabel(field) {
    const label = document.querySelector(`label[for="${field.id}"]`);
    return label ? label.textContent.replace(/\s*\(.*?\)\s*/g, '').trim() : field.name;
}

function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('input[required]');
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Special validations
    if (form.id === 'registerForm') {
        if (!validatePasswordMatch()) {
            isValid = false;
        }
        
        const termsCheckbox = document.getElementById('agreeTerms');
        if (termsCheckbox && !termsCheckbox.checked) {
            showFieldError(termsCheckbox, 'You must accept the Terms of Service and Privacy Policy to create an account');
            isValid = false;
            
            // Scroll to terms checkbox on mobile
            if (window.innerWidth <= 576) {
                termsCheckbox.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }
    
    return isValid;
}

function validateTermsAcceptance() {
    const termsCheckbox = document.getElementById('agreeTerms');
    if (!termsCheckbox) return true;
    
    if (!termsCheckbox.checked) {
        // Show prominent error message
        showMessage('⚠️ Please read and accept our Terms of Service and Privacy Policy to continue.', 'error');
        
        // Highlight the terms section
        const termsGroup = termsCheckbox.closest('.form-group');
        termsGroup.classList.add('highlight');
        
        // Scroll to terms
        termsCheckbox.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Focus on checkbox
        termsCheckbox.focus();
        
        // Show field error
        showFieldError(termsCheckbox, 'You must accept the Terms of Service and Privacy Policy to create an account');
        
        // Remove highlight after animation
        setTimeout(() => {
            termsGroup.classList.remove('highlight');
        }, 3000);
        
        return false;
    }
    
    clearFieldError(termsCheckbox);
    return true;
}

function setupTermsValidation() {
    const termsCheckbox = document.getElementById('agreeTerms');
    if (!termsCheckbox) return;
    
    // Add visual feedback when checked
    termsCheckbox.addEventListener('change', function() {
        const termsGroup = this.closest('.form-group');
        
        if (this.checked) {
            clearFieldError(this);
            termsGroup.style.background = '#c6f6d5';
            termsGroup.style.borderColor = '#9ae6b4';
            
            // Remove success highlight after 2 seconds
            setTimeout(() => {
                termsGroup.style.background = '';
                termsGroup.style.borderColor = '';
            }, 2000);
        } else {
            termsGroup.style.background = '';
            termsGroup.style.borderColor = '';
        }
    });
    
    // Add click handler to links to ensure they work
    const termsLinks = termsCheckbox.closest('.form-group').querySelectorAll('a');
    termsLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Let the link work normally but add analytics if needed
            console.log('Terms/Privacy link clicked:', this.href);
        });
    });
}

async function handleLogin(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (!validateForm(form)) {
        return;
    }
    
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    const rememberMe = formData.get('rememberMe') === 'on';
    
    setButtonLoading(submitBtn, true);
    
    try {
        const result = await window.csrfManager.stateChangingRequest('login', {
            email: email,
            password: password,
            remember_me: rememberMe
        });
        
        if (result.success) {
            showMessage('Login successful! Redirecting...', 'success');
            
            setTimeout(() => {
                if (result.user && result.user.is_admin) {
                    window.location.href = '../admin.php';
                } else {
                    window.location.href = 'user/dashboard.php';
                }
            }, 1000);
        } else {
            showMessage(result.error || 'Login failed. Please check your credentials.', 'error');
            
            // Focus on first field with error
            if (result.field) {
                const field = document.getElementById('login' + result.field.charAt(0).toUpperCase() + result.field.slice(1));
                if (field) field.focus();
            }
        }
    } catch (error) {
        console.error('Login error:', error);
        showMessage('Connection error. Please check your internet connection and try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Check terms acceptance first
    if (!validateTermsAcceptance()) {
        return;
    }
    
    // Then validate other fields
    if (!validateForm(form)) {
        return;
    }
    
    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    const pin = formData.get('pin');
    const referral = formData.get('referral');
    
    setButtonLoading(submitBtn, true);
    
    try {
        const result = await window.csrfManager.stateChangingRequest('register', {
            email: email,
            password: password,
            pin: pin,
            referral: referral
        });
        
        if (result.success) {
            showMessage('Registration successful! Redirecting to your dashboard...', 'success');
            
            setTimeout(() => {
                if (result.user && result.user.is_admin) {
                    window.location.href = '../admin.php';
                } else {
                    window.location.href = 'user/dashboard.php';
                }
            }, 1500);
        } else {
            showMessage(result.error || 'Registration failed. Please try again.', 'error');
            
            // Focus on first field with error
            if (result.field) {
                const field = document.getElementById('register' + result.field.charAt(0).toUpperCase() + result.field.slice(1));
                if (field) field.focus();
            }
        }
    } catch (error) {
        console.error('Registration error:', error);
        showMessage('Connection error. Please check your internet connection and try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function handleForgotPassword(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (!validateForm(form)) {
        return;
    }
    
    const formData = new FormData(form);
    const email = formData.get('email');
    
    setButtonLoading(submitBtn, true);
    
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'forgot_password',
                email: email
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Password reset instructions have been sent to your email address.', 'success');
            form.reset();
        } else {
            showMessage(result.error || 'Failed to send reset email. Please try again.', 'error');
        }
    } catch (error) {
        console.error('Forgot password error:', error);
        showMessage('Connection error. Please check your internet connection and try again.', 'error');
    } finally {
        setButtonLoading(submitBtn, false);
    }
}

async function verifyAndPrefillReferral() {
    const by = getQueryParam('by');
    const referralInput = document.getElementById('registerReferral');
    
    if (!referralInput || !by) return;
    
    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ 
                action: 'verify_referral', 
                referral_code: by 
            })
        });
        
        const data = await response.json();
        
        if (data && data.exists) {
            referralInput.value = by;
            // Show success message for valid referral
            const hint = referralInput.parentElement.parentElement.querySelector('.field-hint');
            if (hint) {
                hint.textContent = '✅ Valid referral code applied!';
                hint.style.color = '#38a169';
            }
        }
    } catch (e) {
        console.warn('Could not verify referral code:', e);
    }
}

function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        button.textContent = 'Please wait...';
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        // Reset button text based on form
        const form = button.closest('form');
        if (form.id === 'loginForm') {
            button.textContent = 'Login';
        } else if (form.id === 'registerForm') {
            button.textContent = 'Register';
        } else if (form.id === 'forgotPasswordForm') {
            button.textContent = 'Send Reset Link';
        }
    }
}

function showMessage(message, type = 'info') {
    const messageEl = document.getElementById('message');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';
        
        // Auto-hide success and info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }
    }
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
