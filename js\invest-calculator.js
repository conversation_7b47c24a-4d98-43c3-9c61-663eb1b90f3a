// Investment calculator functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeCalculator();
    initializeInvestmentFAQ();
});

// Investment plans will be loaded from the select element data attributes
function getPlansFromDOM() {
    const planSelect = document.getElementById('investPlan');
    const plans = {};
    
    if (planSelect) {
        Array.from(planSelect.options).forEach(option => {
            const planId = option.value;
            const dailyReturn = parseFloat(option.dataset.return) || 0;
            const duration = parseInt(option.dataset.duration) || 0;
            
            plans[planId] = {
                name: option.textContent,
                dailyRate: dailyReturn / 100, // Convert percentage to decimal
                duration: duration,
                minAmount: 100, // Will be updated from API if needed
                maxAmount: Infinity
            };
        });
    }
    
    return plans;
}

function initializeCalculator() {
    const amountInput = document.getElementById('investAmount');
    const planSelect = document.getElementById('investPlan');
    
    if (!amountInput || !planSelect) return;
    
    // Initial calculation
    calculateInvestment();
    
    // Update on input changes
    amountInput.addEventListener('input', debounce(calculateInvestment, 300));
    planSelect.addEventListener('change', calculateInvestment);
    
    // Validate amount on blur
    amountInput.addEventListener('blur', validateAmount);
}

function calculateInvestment() {
    const amountInput = document.getElementById('investAmount');
    const planSelect = document.getElementById('investPlan');
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedPlan = planSelect.value;
    const investmentPlans = getPlansFromDOM();
    const plan = investmentPlans[selectedPlan];
    
    if (!plan || amount <= 0) {
        clearResults();
        return;
    }
    
    // Validate amount against plan limits
    if (amount < plan.minAmount || amount > plan.maxAmount) {
        showAmountError(plan);
        clearResults();
        return;
    }
    
    hideAmountError();
    
    // Calculate returns
    const dailyReturn = amount * plan.dailyRate;
    const totalReturn = dailyReturn * plan.duration;
    const totalReceived = amount + totalReturn;
    const profitPercentage = (totalReturn / amount) * 100;
    
    // Update display
    updateResults({
        dailyReturn,
        totalReturn,
        totalReceived,
        profitPercentage
    });
}

function updateResults(results) {
    const elements = {
        dailyReturn: document.getElementById('dailyReturn'),
        totalReturn: document.getElementById('totalReturn'),
        totalReceived: document.getElementById('totalReceived'),
        profitPercentage: document.getElementById('profitPercentage')
    };
    
    if (elements.dailyReturn) {
        elements.dailyReturn.textContent = formatCurrency(results.dailyReturn);
    }
    
    if (elements.totalReturn) {
        elements.totalReturn.textContent = formatCurrency(results.totalReturn);
    }
    
    if (elements.totalReceived) {
        elements.totalReceived.textContent = formatCurrency(results.totalReceived);
    }
    
    if (elements.profitPercentage) {
        elements.profitPercentage.textContent = `${results.profitPercentage.toFixed(0)}%`;
    }
    
    // Add animation effect
    Object.values(elements).forEach(el => {
        if (el) {
            el.style.transform = 'scale(1.05)';
            setTimeout(() => {
                el.style.transform = 'scale(1)';
            }, 200);
        }
    });
}

function clearResults() {
    const elements = {
        dailyReturn: document.getElementById('dailyReturn'),
        totalReturn: document.getElementById('totalReturn'),
        totalReceived: document.getElementById('totalReceived'),
        profitPercentage: document.getElementById('profitPercentage')
    };
    
    Object.values(elements).forEach(el => {
        if (el) {
            el.textContent = '0 USDT';
        }
    });
    
    if (elements.profitPercentage) {
        elements.profitPercentage.textContent = '0%';
    }
}

function validateAmount() {
    const amountInput = document.getElementById('investAmount');
    const planSelect = document.getElementById('investPlan');
    
    const amount = parseFloat(amountInput.value) || 0;
    const selectedPlan = planSelect.value;
    const plan = investmentPlans[selectedPlan];
    
    if (amount < plan.minAmount) {
        amountInput.value = plan.minAmount;
        calculateInvestment();
    } else if (amount > plan.maxAmount && plan.maxAmount !== Infinity) {
        amountInput.value = plan.maxAmount;
        calculateInvestment();
    }
}

function showAmountError(plan) {
    hideAmountError();
    
    const amountInput = document.getElementById('investAmount');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'amount-error';
    errorDiv.id = 'amountError';
    
    let errorMessage;
    if (plan.maxAmount === Infinity) {
        errorMessage = `Minimum investment: ${formatCurrency(plan.minAmount)}`;
    } else {
        errorMessage = `Investment range: ${formatCurrency(plan.minAmount)} - ${formatCurrency(plan.maxAmount)}`;
    }
    
    errorDiv.innerHTML = `
        <span style="color: #dc3545; font-size: 0.9rem; font-weight: 500;">
            ${errorMessage}
        </span>
    `;
    
    amountInput.parentNode.appendChild(errorDiv);
    amountInput.style.borderColor = '#dc3545';
}

function hideAmountError() {
    const errorDiv = document.getElementById('amountError');
    const amountInput = document.getElementById('investAmount');
    
    if (errorDiv) {
        errorDiv.remove();
    }
    
    if (amountInput) {
        amountInput.style.borderColor = '';
    }
}

// FAQ functionality for investment page
function initializeInvestmentFAQ() {
    const faqItems = document.querySelectorAll('.investment-faq .faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        if (question) {
            question.addEventListener('click', function() {
                // Close other items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        otherItem.classList.remove('active');
                    }
                });
                
                // Toggle current item
                item.classList.toggle('active');
            });
        }
    });
}

// Utility functions
function formatCurrency(amount, currency = 'USDT') {
    return `${formatNumber(Math.round(amount * 100) / 100)} ${currency}`;
}

function formatNumber(num) {
    return num.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Investment plan comparison functionality
function initializePlanComparison() {
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) return;
    
    amountInput.addEventListener('input', updatePlanExamples);
}

function updatePlanExamples() {
    const amount = parseFloat(document.getElementById('investAmount')?.value) || 1000;
    
    Object.keys(investmentPlans).forEach(planKey => {
        const plan = investmentPlans[planKey];
        const exampleCard = document.querySelector(`.plan-card.${planKey} .plan-example`);
        
        if (exampleCard && amount >= plan.minAmount && amount <= plan.maxAmount) {
            const dailyReturn = amount * plan.dailyRate;
            const totalProfit = dailyReturn * plan.duration;
            const totalReceived = amount + totalProfit;
            
            const breakdown = exampleCard.querySelector('.example-breakdown');
            if (breakdown) {
                breakdown.innerHTML = `
                    <div class="breakdown-item">
                        <span>Daily Return:</span>
                        <span>${formatCurrency(dailyReturn)}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>Total Profit:</span>
                        <span>${formatCurrency(totalProfit)}</span>
                    </div>
                    <div class="breakdown-item total">
                        <span>You Receive:</span>
                        <span>${formatCurrency(totalReceived)}</span>
                    </div>
                `;
            }
            
            const exampleTitle = exampleCard.querySelector('h4');
            if (exampleTitle) {
                exampleTitle.textContent = `Example: ${formatCurrency(amount)} Investment`;
            }
        }
    });
}

// Plan recommendation based on amount
function getRecommendedPlan(amount) {
    for (const [key, plan] of Object.entries(investmentPlans)) {
        if (amount >= plan.minAmount && amount <= plan.maxAmount) {
            return key;
        }
    }
    return 'starter'; // Default fallback
}

// Highlight recommended plan
function highlightRecommendedPlan() {
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) return;
    
    const amount = parseFloat(amountInput.value) || 0;
    const recommendedPlan = getRecommendedPlan(amount);
    
    // Remove previous highlights
    document.querySelectorAll('.plan-card').forEach(card => {
        card.classList.remove('recommended');
    });
    
    // Add highlight to recommended plan
    const recommendedCard = document.querySelector(`.plan-card.${recommendedPlan}`);
    if (recommendedCard && amount > 0) {
        recommendedCard.classList.add('recommended');
    }
}

// Initialize plan comparison when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializePlanComparison();
    
    // Update plan highlights when amount changes
    const amountInput = document.getElementById('investAmount');
    if (amountInput) {
        amountInput.addEventListener('input', debounce(highlightRecommendedPlan, 300));
    }
});

// Add CSS for recommended plan highlighting
const highlightCSS = `
<style>
    .plan-card.recommended {
        border-color: #ffd700 !important;
        box-shadow: 0 8px 30px rgba(255, 215, 0, 0.3) !important;
        position: relative;
    }
    
    .plan-card.recommended::before {
        content: "Recommended for your amount";
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        background: #ffd700;
        color: #000;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        white-space: nowrap;
    }
    
    .amount-error {
        margin-top: 8px;
    }
    
    .calc-results .result-value {
        transition: transform 0.2s ease;
    }
</style>`;

document.head.insertAdjacentHTML('beforeend', highlightCSS);
