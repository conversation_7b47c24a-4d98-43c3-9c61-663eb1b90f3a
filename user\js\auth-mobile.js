// Enhanced Mobile Authentication JavaScript for TLS Wallet
document.addEventListener('DOMContentLoaded', function() {
    initMobileAuth();
});

let currentRegistrationStep = 1;
const totalRegistrationSteps = 3;

function initMobileAuth() {
    setupMobileFormHandlers();
    setupMobileValidation();
    setupMobilePasswordToggles();
    setupMobileRegistrationFlow();
    setupMobileTouchInteractions();
    setupMobileAccessibility();
    setupMobileSwipeGestures();
    
    // Initialize with login form
    showMobileLogin();
    
    // Setup mobile-specific features
    setupMobileKeyboard();
    setupMobileViewport();
    
    // Verify and prefill referral code if present
    verifyAndPrefillMobileReferral();
}

function setupMobileFormHandlers() {
    const loginForm = document.getElementById('mobileLoginForm');
    const registerForm = document.getElementById('mobileRegisterForm');
    const forgotPasswordForm = document.getElementById('mobileForgotPasswordForm');
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleMobileLogin);
    }
    
    if (registerForm) {
        registerForm.addEventListener('submit', handleMobileRegister);
    }
    
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleMobileForgotPassword);
    }
}

function setupMobileValidation() {
    // Real-time validation for mobile forms
    const inputs = document.querySelectorAll('.mobile-input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', () => validateMobileField(input));
        input.addEventListener('input', () => {
            clearMobileFieldError(input);
            updateMobileInputStatus(input);
        });
        
        // Special handling for password fields
        if (input.type === 'password') {
            input.addEventListener('input', () => {
                if (input.id === 'mobileRegisterPassword') {
                    updateMobilePasswordStrength(input.value);
                }
                if (input.id === 'mobileConfirmPassword') {
                    validateMobilePasswordMatch();
                }
            });
        }
        
        // Email validation with mobile-specific feedback
        if (input.type === 'email') {
            input.addEventListener('input', () => {
                if (input.value && !isValidEmail(input.value)) {
                    showMobileFieldError(input, 'Please enter a valid email address');
                    updateInputStatus(input, 'invalid');
                } else if (input.value) {
                    clearMobileFieldError(input);
                    updateInputStatus(input, 'valid');
                }
            });
        }
        
        // PIN validation with mobile keyboard
        if (input.name === 'pin') {
            input.addEventListener('input', (e) => {
                // Only allow digits
                e.target.value = e.target.value.replace(/\D/g, '');
                
                if (e.target.value.length > 0 && e.target.value.length < 5) {
                    showMobileFieldError(input, 'PIN must be exactly 5 digits');
                    updateInputStatus(input, 'invalid');
                } else if (e.target.value.length === 5) {
                    clearMobileFieldError(input);
                    updateInputStatus(input, 'valid');
                }
            });
            
            // Set numeric keyboard on mobile
            input.setAttribute('inputmode', 'numeric');
            input.setAttribute('pattern', '[0-9]*');
        }
    });
    
    // Terms checkbox validation
    const termsCheckbox = document.getElementById('mobileAgreeTerms');
    if (termsCheckbox) {
        termsCheckbox.addEventListener('change', () => {
            if (!termsCheckbox.checked) {
                showMobileFieldError(termsCheckbox, 'You must accept the Terms of Service and Privacy Policy');
            } else {
                clearMobileFieldError(termsCheckbox);
                // Add visual feedback
                const termsGroup = termsCheckbox.closest('.mobile-form-group');
                termsGroup.style.background = 'rgba(56, 161, 105, 0.1)';
                termsGroup.style.borderColor = 'rgba(56, 161, 105, 0.3)';
                
                setTimeout(() => {
                    termsGroup.style.background = '';
                    termsGroup.style.borderColor = '';
                }, 2000);
            }
        });
    }
}

function setupMobilePasswordToggles() {
    const toggles = document.querySelectorAll('.mobile-password-toggle');
    toggles.forEach(toggle => {
        toggle.addEventListener('click', (e) => {
            e.preventDefault();
            const input = toggle.parentElement.querySelector('.mobile-input');
            toggleMobilePassword(input.id);
        });
    });
}

function setupMobileRegistrationFlow() {
    // Initialize registration progress
    updateRegistrationProgress();
}

function setupMobileTouchInteractions() {
    // Add touch feedback to buttons
    const buttons = document.querySelectorAll('.mobile-btn');
    buttons.forEach(button => {
        button.addEventListener('touchstart', () => {
            button.style.transform = 'scale(0.98)';
        });
        
        button.addEventListener('touchend', () => {
            setTimeout(() => {
                button.style.transform = '';
            }, 150);
        });
    });
    
    // Add haptic feedback if supported
    if ('vibrate' in navigator) {
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                navigator.vibrate(10); // Short vibration
            });
        });
    }
}

function setupMobileAccessibility() {
    // Enhanced keyboard navigation for mobile
    const tabButtons = document.querySelectorAll('.mobile-tab-btn');
    tabButtons.forEach((tab, index) => {
        tab.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                e.preventDefault();
                const nextIndex = e.key === 'ArrowRight' ? 
                    (index + 1) % tabButtons.length : 
                    (index - 1 + tabButtons.length) % tabButtons.length;
                tabButtons[nextIndex].focus();
                tabButtons[nextIndex].click();
            }
        });
    });
    
    // Announce form changes to screen readers
    const forms = document.querySelectorAll('.mobile-auth-form');
    forms.forEach(form => {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (form.classList.contains('active')) {
                        announceToScreenReader(`${form.id} form is now active`);
                    }
                }
            });
        });
        
        observer.observe(form, { attributes: true });
    });
}

function setupMobileSwipeGestures() {
    let startX = 0;
    let startY = 0;
    
    const authContainer = document.querySelector('.mobile-auth-container');
    
    authContainer.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });
    
    authContainer.addEventListener('touchend', (e) => {
        if (!startX || !startY) return;
        
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        
        const diffX = startX - endX;
        const diffY = startY - endY;
        
        // Only process horizontal swipes
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            if (diffX > 0) {
                // Swipe left - next form
                const currentForm = document.querySelector('.mobile-auth-form.active');
                if (currentForm.id === 'mobileLoginForm') {
                    showMobileRegister();
                }
            } else {
                // Swipe right - previous form
                const currentForm = document.querySelector('.mobile-auth-form.active');
                if (currentForm.id === 'mobileRegisterForm') {
                    showMobileLogin();
                }
            }
        }
        
        startX = 0;
        startY = 0;
    });
}

function setupMobileKeyboard() {
    // Handle virtual keyboard on mobile
    const viewport = window.visualViewport;
    
    if (viewport) {
        viewport.addEventListener('resize', () => {
            const authContainer = document.querySelector('.mobile-auth-container');
            if (viewport.height < window.innerHeight * 0.75) {
                // Keyboard is likely open
                authContainer.style.paddingBottom = '20px';
                authContainer.style.height = `${viewport.height}px`;
            } else {
                // Keyboard is likely closed
                authContainer.style.paddingBottom = '';
                authContainer.style.height = '';
            }
        });
    }
}

function setupMobileViewport() {
    // Prevent zoom on input focus for iOS
    const inputs = document.querySelectorAll('.mobile-input');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            if (window.innerWidth < 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }
        });
        
        input.addEventListener('blur', () => {
            if (window.innerWidth < 768) {
                const viewport = document.querySelector('meta[name="viewport"]');
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no');
            }
        });
    });
}

// Mobile Form Navigation Functions
function showMobileLogin() {
    hideAllMobileForms();
    document.getElementById('mobileLoginForm').classList.add('active');
    setMobileActiveTab(0);
    updateProgressStep('login');
    
    // Focus first input with delay for animation
    setTimeout(() => {
        document.getElementById('mobileLoginEmail').focus();
    }, 300);
}

function showMobileRegister() {
    hideAllMobileForms();
    document.getElementById('mobileRegisterForm').classList.add('active');
    setMobileActiveTab(1);
    updateProgressStep('register');
    currentRegistrationStep = 1;
    showRegistrationStep(1);
    
    setTimeout(() => {
        document.getElementById('mobileRegisterEmail').focus();
    }, 300);
}

function showMobileForgotPassword() {
    hideAllMobileForms();
    document.getElementById('mobileForgotPasswordForm').classList.add('active');
    document.querySelectorAll('.mobile-tab-btn').forEach(btn => btn.classList.remove('active'));
    updateProgressStep('forgot');
    
    setTimeout(() => {
        document.getElementById('mobileForgotEmail').focus();
    }, 300);
}

function hideAllMobileForms() {
    document.querySelectorAll('.mobile-auth-form').forEach(form => {
        form.classList.remove('active');
    });
}

function setMobileActiveTab(index) {
    document.querySelectorAll('.mobile-tab-btn').forEach((btn, i) => {
        btn.classList.toggle('active', i === index);
        btn.setAttribute('aria-selected', i === index);
    });
}

function updateProgressStep(step) {
    document.querySelectorAll('.progress-step').forEach(stepEl => {
        stepEl.classList.remove('active');
    });
    
    const activeStep = document.querySelector(`[data-step="${step}"]`);
    if (activeStep) {
        activeStep.classList.add('active');
    }
}

// Mobile Registration Step Management
function nextRegistrationStep() {
    if (currentRegistrationStep < totalRegistrationSteps) {
        if (validateCurrentRegistrationStep()) {
            currentRegistrationStep++;
            showRegistrationStep(currentRegistrationStep);
            updateRegistrationProgress();
        }
    }
}

function previousRegistrationStep() {
    if (currentRegistrationStep > 1) {
        currentRegistrationStep--;
        showRegistrationStep(currentRegistrationStep);
        updateRegistrationProgress();
    }
}

function showRegistrationStep(step) {
    document.querySelectorAll('.registration-step').forEach(stepEl => {
        stepEl.classList.remove('active');
    });
    
    const targetStep = document.getElementById(`step${step}`);
    if (targetStep) {
        targetStep.classList.add('active');
        
        // Focus first input in step
        const firstInput = targetStep.querySelector('.mobile-input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 300);
        }
    }
    
    // Update step labels
    document.querySelectorAll('.step-label').forEach((label, index) => {
        label.classList.toggle('active', index + 1 === step);
    });
}

function updateRegistrationProgress() {
    const progressBar = document.getElementById('registrationProgress');
    if (progressBar) {
        const progressPercent = (currentRegistrationStep / totalRegistrationSteps) * 100;
        progressBar.style.width = `${progressPercent}%`;
    }
}

function validateCurrentRegistrationStep() {
    const currentStepEl = document.getElementById(`step${currentRegistrationStep}`);
    if (!currentStepEl) return true;
    
    const requiredInputs = currentStepEl.querySelectorAll('.mobile-input[required]');
    let isValid = true;
    
    requiredInputs.forEach(input => {
        if (!validateMobileField(input)) {
            isValid = false;
        }
    });
    
    // Special validation for step 1 (password confirmation)
    if (currentRegistrationStep === 1) {
        if (!validateMobilePasswordMatch()) {
            isValid = false;
        }
    }
    
    return isValid;
}

// Mobile Password Functions
function toggleMobilePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.parentElement.querySelector('.mobile-password-toggle');
    const icon = toggle.querySelector('svg');

    if (field.type === 'password') {
        field.type = 'text';
        icon.innerHTML = '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/><line x1="1" y1="1" x2="23" y2="23"/>';
        toggle.setAttribute('aria-label', 'Hide password');
    } else {
        field.type = 'password';
        icon.innerHTML = '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/>';
        toggle.setAttribute('aria-label', 'Show password');
    }

    // Add haptic feedback
    if ('vibrate' in navigator) {
        navigator.vibrate(5);
    }
}

function updateMobilePasswordStrength(password) {
    const strengthIndicator = document.getElementById('mobilePasswordStrength');
    if (!strengthIndicator) return;

    const strength = calculatePasswordStrength(password);

    strengthIndicator.className = `mobile-password-strength ${strength.level}`;

    const strengthText = strengthIndicator.querySelector('.strength-text');
    if (strengthText) {
        const strengthLabels = {
            weak: 'Weak password',
            medium: 'Good password',
            strong: 'Strong password'
        };
        strengthText.textContent = strengthLabels[strength.level] || 'Password strength';
    }

    strengthIndicator.setAttribute('aria-label', `Password strength: ${strength.level}`);
}

function validateMobilePasswordMatch() {
    const password = document.getElementById('mobileRegisterPassword');
    const confirmPassword = document.getElementById('mobileConfirmPassword');

    if (!password || !confirmPassword) return true;

    if (confirmPassword.value && password.value !== confirmPassword.value) {
        showMobileFieldError(confirmPassword, 'Passwords do not match');
        updateInputStatus(confirmPassword, 'invalid');
        return false;
    } else if (confirmPassword.value) {
        clearMobileFieldError(confirmPassword);
        updateInputStatus(confirmPassword, 'valid');
        return true;
    }

    return true;
}

// Mobile Validation Functions
function validateMobileField(field) {
    clearMobileFieldError(field);

    if (!field.value.trim() && field.hasAttribute('required')) {
        showMobileFieldError(field, `${getMobileFieldLabel(field)} is required`);
        updateInputStatus(field, 'invalid');
        return false;
    }

    // Specific validations
    switch (field.type) {
        case 'email':
            if (field.value && !isValidEmail(field.value)) {
                showMobileFieldError(field, 'Please enter a valid email address');
                updateInputStatus(field, 'invalid');
                return false;
            }
            break;

        case 'password':
            if (field.name === 'password' && field.value.length < 6) {
                showMobileFieldError(field, 'Password must be at least 6 characters long');
                updateInputStatus(field, 'invalid');
                return false;
            }
            break;
    }

    // PIN validation
    if (field.name === 'pin') {
        if (field.value.length !== 5 || !/^\d{5}$/.test(field.value)) {
            showMobileFieldError(field, 'PIN must be exactly 5 digits');
            updateInputStatus(field, 'invalid');
            return false;
        }
    }

    updateInputStatus(field, 'valid');
    return true;
}

function showMobileFieldError(field, message) {
    const errorId = field.id + 'Error';
    const errorElement = document.getElementById(errorId);

    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'flex';
    }

    field.setAttribute('aria-invalid', 'true');
    field.setAttribute('aria-describedby', errorId);
    field.classList.add('error');

    // Add visual feedback
    field.style.borderColor = 'var(--mobile-danger)';
    field.style.boxShadow = '0 0 0 4px rgba(229, 62, 62, 0.1)';

    // Haptic feedback for errors
    if ('vibrate' in navigator) {
        navigator.vibrate([50, 50, 50]);
    }
}

function clearMobileFieldError(field) {
    const errorId = field.id + 'Error';
    const errorElement = document.getElementById(errorId);

    if (errorElement) {
        errorElement.textContent = '';
        errorElement.style.display = 'none';
    }

    field.removeAttribute('aria-invalid');
    field.removeAttribute('aria-describedby');
    field.classList.remove('error');
    field.style.borderColor = '';
    field.style.boxShadow = '';
}

function updateInputStatus(field, status) {
    const statusElement = field.parentElement.querySelector('.input-status');
    if (!statusElement) return;

    statusElement.className = `input-status ${status}`;

    if (status === 'valid') {
        statusElement.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"/></svg>';
    } else if (status === 'invalid') {
        statusElement.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/></svg>';
    } else {
        statusElement.innerHTML = '';
    }
}

function updateMobileInputStatus(field) {
    if (field.value.trim()) {
        if (field.type === 'email') {
            updateInputStatus(field, isValidEmail(field.value) ? 'valid' : 'invalid');
        } else if (field.name === 'pin') {
            updateInputStatus(field, field.value.length === 5 ? 'valid' : 'invalid');
        } else if (field.value.length >= 6) {
            updateInputStatus(field, 'valid');
        }
    } else {
        updateInputStatus(field, '');
    }
}

function getMobileFieldLabel(field) {
    const label = document.querySelector(`label[for="${field.id}"] .label-text`);
    return label ? label.textContent.trim() : field.name;
}

function validateMobileForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('.mobile-input[required]');

    requiredFields.forEach(field => {
        if (!validateMobileField(field)) {
            isValid = false;
        }
    });

    // Special validations
    if (form.id === 'mobileRegisterForm') {
        if (!validateMobilePasswordMatch()) {
            isValid = false;
        }

        const termsCheckbox = document.getElementById('mobileAgreeTerms');
        if (termsCheckbox && !termsCheckbox.checked) {
            showMobileFieldError(termsCheckbox, 'You must accept the Terms of Service and Privacy Policy');
            isValid = false;

            // Scroll to terms checkbox
            termsCheckbox.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    return isValid;
}

// Mobile Form Submission Handlers
async function handleMobileLogin(e) {
    e.preventDefault();

    const form = e.target;
    const submitBtn = form.querySelector('.mobile-btn');

    if (!validateMobileForm(form)) {
        return;
    }

    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    const rememberMe = formData.get('rememberMe') === 'on';

    setMobileButtonLoading(submitBtn, true);

    try {
        const result = await window.csrfManager.stateChangingRequest('login', {
            email: email,
            password: password,
            remember_me: rememberMe
        });

        if (result.success) {
            showMobileMessage('Login successful! Redirecting...', 'success');

            // Haptic feedback for success
            if ('vibrate' in navigator) {
                navigator.vibrate([100, 50, 100]);
            }

            setTimeout(() => {
                if (result.user && result.user.is_admin) {
                    window.location.href = '../admin.php';
                } else {
                    window.location.href = 'user/dashboard.php';
                }
            }, 1000);
        } else {
            showMobileMessage(result.error || 'Login failed. Please check your credentials.', 'error');

            // Focus on first field with error
            if (result.field) {
                const field = document.getElementById('mobileLogin' + result.field.charAt(0).toUpperCase() + result.field.slice(1));
                if (field) {
                    field.focus();
                    field.select();
                }
            }
        }
    } catch (error) {
        console.error('Login error:', error);
        showMobileMessage('Connection error. Please check your internet connection and try again.', 'error');
    } finally {
        setMobileButtonLoading(submitBtn, false);
    }
}

async function handleMobileRegister(e) {
    e.preventDefault();

    const form = e.target;
    const submitBtn = form.querySelector('.mobile-btn[type="submit"]');

    // Validate all steps
    let allStepsValid = true;
    for (let step = 1; step <= totalRegistrationSteps; step++) {
        const stepEl = document.getElementById(`step${step}`);
        const requiredInputs = stepEl.querySelectorAll('.mobile-input[required]');

        requiredInputs.forEach(input => {
            if (!validateMobileField(input)) {
                allStepsValid = false;
            }
        });
    }

    // Check terms acceptance
    const termsCheckbox = document.getElementById('mobileAgreeTerms');
    if (!termsCheckbox || !termsCheckbox.checked) {
        showMobileMessage('Please accept the Terms of Service and Privacy Policy to continue.', 'error');
        showRegistrationStep(3); // Go to terms step
        return;
    }

    if (!allStepsValid) {
        showMobileMessage('Please complete all required fields correctly.', 'error');
        return;
    }

    const formData = new FormData(form);
    const email = formData.get('email');
    const password = formData.get('password');
    const pin = formData.get('pin');
    const referral = formData.get('referral');

    setMobileButtonLoading(submitBtn, true);

    try {
        const result = await window.csrfManager.stateChangingRequest('register', {
            email: email,
            password: password,
            pin: pin,
            referral: referral
        });

        if (result.success) {
            showMobileMessage('Registration successful! Welcome to TLS Wallet!', 'success');

            // Haptic feedback for success
            if ('vibrate' in navigator) {
                navigator.vibrate([100, 50, 100, 50, 100]);
            }

            setTimeout(() => {
                if (result.user && result.user.is_admin) {
                    window.location.href = '../admin.php';
                } else {
                    window.location.href = 'user/dashboard.php';
                }
            }, 1500);
        } else {
            showMobileMessage(result.error || 'Registration failed. Please try again.', 'error');

            // Focus on first field with error
            if (result.field) {
                const field = document.getElementById('mobileRegister' + result.field.charAt(0).toUpperCase() + result.field.slice(1));
                if (field) {
                    field.focus();
                    field.select();
                }
            }
        }
    } catch (error) {
        console.error('Registration error:', error);
        showMobileMessage('Connection error. Please check your internet connection and try again.', 'error');
    } finally {
        setMobileButtonLoading(submitBtn, false);
    }
}

async function handleMobileForgotPassword(e) {
    e.preventDefault();

    const form = e.target;
    const submitBtn = form.querySelector('.mobile-btn');

    if (!validateMobileForm(form)) {
        return;
    }

    const formData = new FormData(form);
    const email = formData.get('email');

    setMobileButtonLoading(submitBtn, true);

    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'forgot_password',
                email: email
            })
        });

        const result = await response.json();

        if (result.success) {
            showMobileMessage('Password reset instructions have been sent to your email address.', 'success');
            form.reset();

            // Update reset steps visual feedback
            updateResetSteps(2);

            // Haptic feedback
            if ('vibrate' in navigator) {
                navigator.vibrate([100, 50, 100]);
            }
        } else {
            showMobileMessage(result.error || 'Failed to send reset email. Please try again.', 'error');
        }
    } catch (error) {
        console.error('Forgot password error:', error);
        showMobileMessage('Connection error. Please check your internet connection and try again.', 'error');
    } finally {
        setMobileButtonLoading(submitBtn, false);
    }
}

function updateResetSteps(activeStep) {
    document.querySelectorAll('.reset-step').forEach((step, index) => {
        step.classList.toggle('active', index + 1 === activeStep);
    });
}

// Mobile Referral Functions
async function verifyAndPrefillMobileReferral() {
    const by = getQueryParam('by');
    const referralInput = document.getElementById('mobileRegisterReferral');

    if (!referralInput || !by) return;

    try {
        const response = await fetch('ajax.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'verify_referral',
                referral_code: by
            })
        });

        const data = await response.json();

        if (data && data.exists) {
            referralInput.value = by;
            updateInputStatus(referralInput, 'valid');

            // Show success message for valid referral
            const hint = referralInput.parentElement.parentElement.querySelector('.mobile-field-hint');
            if (hint) {
                hint.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"/></svg>Valid referral code applied! You\'ll receive bonus rewards.';
                hint.style.color = 'var(--mobile-accent)';
                hint.style.background = 'rgba(56, 161, 105, 0.1)';
                hint.style.border = '1px solid rgba(56, 161, 105, 0.2)';
            }
        }
    } catch (e) {
        console.warn('Could not verify referral code:', e);
    }
}

// Mobile UI Helper Functions
function setMobileButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');

        const btnText = button.querySelector('.btn-text');
        const btnLoader = button.querySelector('.btn-loader');

        if (btnText) btnText.style.opacity = '0.7';
        if (btnLoader) btnLoader.style.display = 'block';

    } else {
        button.disabled = false;
        button.classList.remove('loading');

        const btnText = button.querySelector('.btn-text');
        const btnLoader = button.querySelector('.btn-loader');

        if (btnText) btnText.style.opacity = '1';
        if (btnLoader) btnLoader.style.display = 'none';
    }
}

function showMobileMessage(message, type = 'info') {
    const messageEl = document.getElementById('mobileMessage');
    if (messageEl) {
        messageEl.textContent = message;
        messageEl.className = `mobile-message ${type}`;
        messageEl.style.display = 'flex';

        // Scroll message into view
        messageEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

        // Auto-hide success and info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        // Announce to screen readers
        announceToScreenReader(message);
    }
}

function announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.style.position = 'absolute';
    announcement.style.left = '-10000px';
    announcement.style.width = '1px';
    announcement.style.height = '1px';
    announcement.style.overflow = 'hidden';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

// Utility Functions (reuse from main auth.js)
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function calculatePasswordStrength(password) {
    let score = 0;

    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score < 3) return { level: 'weak', score };
    if (score < 5) return { level: 'medium', score };
    return { level: 'strong', score };
}

// Mobile-specific enhancements
function initMobilePWA() {
    // Add to home screen prompt
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;

        // Show custom install button if desired
        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.style.display = 'block';
            installBtn.addEventListener('click', () => {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                });
            });
        }
    });
}

// Initialize PWA features
if ('serviceWorker' in navigator) {
    initMobilePWA();
}

// Export functions for global access
window.mobileAuth = {
    showMobileLogin,
    showMobileRegister,
    showMobileForgotPassword,
    toggleMobilePassword,
    nextRegistrationStep,
    previousRegistrationStep
};
