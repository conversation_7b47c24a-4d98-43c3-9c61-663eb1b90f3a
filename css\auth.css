/* Authentication page styles - Enhanced with Dark Mode Support */

/* CSS Variables for auth page */
:root {
    /* Light mode auth colors */
    --auth-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --auth-nav-bg: rgba(255, 255, 255, 0.1);
    --auth-nav-border: rgba(255, 255, 255, 0.1);
    --auth-nav-text: white;
    --auth-nav-text-muted: rgba(255, 255, 255, 0.9);
    --auth-nav-hover-bg: rgba(255, 255, 255, 0.15);
    --auth-card-bg: rgba(255, 255, 255, 0.15);
    --auth-card-border: rgba(255, 255, 255, 0.2);
    --auth-title-gradient: linear-gradient(45deg, #ffffff, #f0f0f0);
    --auth-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    --auth-title-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Dark mode auth colors */
@media (prefers-color-scheme: dark) {
    :root {
        --auth-bg-gradient: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        --auth-nav-bg: rgba(0, 0, 0, 0.3);
        --auth-nav-border: rgba(255, 255, 255, 0.1);
        --auth-nav-text: #f7fafc;
        --auth-nav-text-muted: rgba(255, 255, 255, 0.8);
        --auth-nav-hover-bg: rgba(255, 255, 255, 0.1);
        --auth-card-bg: rgba(0, 0, 0, 0.3);
        --auth-card-border: rgba(255, 255, 255, 0.1);
        --auth-title-gradient: linear-gradient(45deg, #f7fafc, #e2e8f0);
        --auth-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
        --auth-title-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }
}

/* Public Navigation */
.public-nav {
    background: var(--auth-nav-bg);
    backdrop-filter: blur(10px);
    padding: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    border-bottom: 1px solid var(--auth-nav-border);
}

.public-nav .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
}

.public-nav .nav-brand h2 {
    color: var(--auth-nav-text);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: var(--auth-title-shadow);
}

.public-nav .nav-links {
    display: flex;
    gap: 8px;
}

.public-nav .nav-link {
    color: var(--auth-nav-text-muted);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 14px;
}

.public-nav .nav-link:hover {
    color: var(--auth-nav-text);
    background: var(--auth-nav-hover-bg);
    transform: translateY(-1px);
}

/* Main Container */
.auth-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 90px 20px 40px;
    background: var(--auth-bg-gradient);
    position: relative;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.auth-wrapper {
    width: 100%;
    max-width: 420px;
    position: relative;
    z-index: 1;
}

/* Header Styles */
.auth-header {
    text-align: center;
    margin-bottom: 32px;
    color: var(--auth-nav-text);
}

.auth-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.logo-icon {
    font-size: 3rem;
    background: var(--auth-card-bg);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2px solid var(--auth-card-border);
}

.auth-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: var(--auth-title-shadow);
    background: var(--auth-title-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 400;
    margin: 0;
    text-shadow: var(--auth-text-shadow);
}

/* Tab Styles */
.auth-tabs {
    display: flex;
    background: var(--auth-card-bg);
    border-radius: 12px;
    padding: 6px;
    margin-bottom: 24px;
    backdrop-filter: blur(15px);
    border: 1px solid var(--auth-card-border);
}

.tab-btn {
    flex: 1;
    padding: 14px 20px;
    border: none;
    background: transparent;
    color: var(--auth-nav-text-muted);
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 15px;
}

.tab-btn .tab-icon {
    font-size: 1.1em;
}

.tab-btn.active {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 4px 12px var(--shadow-light);
    transform: translateY(-1px);
}

.tab-btn:hover:not(.active) {
    background: var(--auth-nav-hover-bg);
    color: var(--auth-nav-text);
}

/* Form Styles */
.auth-form {
    background: var(--auth-card-bg);
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 20px 40px var(--shadow-strong);
    width: 100%;
    display: none;
    border: 1px solid var(--auth-card-border);
}

.auth-form.active {
    display: block;
    animation: slideIn 0.4s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.form-header {
    text-align: center;
    margin-bottom: 24px;
}

.form-header h2 {
    color: #2d3748;
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 6px 0;
}

.form-header p {
    color: #718096;
    font-size: 0.95rem;
    margin: 0;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 6px;
    display: block;
    font-size: 14px;
}

.optional {
    color: #a0aec0;
    font-weight: 400;
    font-size: 13px;
}

/* Input Wrapper */
.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    font-size: 1.1em;
    color: #a0aec0;
    z-index: 2;
}

.auth-form input {
    width: 100%;
    padding: 14px 16px 14px 48px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #f7fafc;
    color: #2d3748;
}

.auth-form input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.auth-form input::placeholder {
    color: #a0aec0;
    font-size: 15px;
}

.password-toggle {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    z-index: 2;
}

.password-toggle:hover {
    background-color: #f7fafc;
}

/* Field Validation */
.field-error {
    color: #e53e3e;
    font-size: 13px;
    margin-top: 4px;
    min-height: 0;
    display: none;
    align-items: center;
    gap: 4px;
    line-height: 1.3;
}

.field-error::before {
    content: '⚠️';
    font-size: 12px;
    display: none;
}

.field-error:not(:empty) {
    display: flex;
    min-height: 16px;
}

.field-error:not(:empty)::before {
    display: inline;
}

.field-hint {
    color: #718096;
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

/* Password Strength */
.password-strength {
    margin-top: 8px;
    height: 4px;
    background-color: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.password-strength.weak {
    background: linear-gradient(90deg, #e53e3e 0%, #e53e3e 33%, #e2e8f0 33%);
}

.password-strength.medium {
    background: linear-gradient(90deg, #ed8936 0%, #ed8936 66%, #e2e8f0 66%);
}

.password-strength.strong {
    background: linear-gradient(90deg, #38a169 0%, #38a169 100%);
}

/* Checkbox Styles */
.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    background-color: #f7fafc;
    transition: all 0.3s ease;
    flex-shrink: 0;
    position: relative;
    margin-top: 2px;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.terms-checkbox {
    color: #4a5568;
    background: #f7fafc;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 1.5;
}

.terms-checkbox:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.terms-checkbox a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.terms-checkbox a:hover {
    text-decoration: underline;
    color: #5a6fd8;
}

.terms-checkbox input[type="checkbox"]:checked + .checkmark {
    background-color: #38a169;
    border-color: #38a169;
}

/* Highlight terms when required */
.form-group.highlight {
    background: #fed7d7 !important;
    border: 2px solid #feb2b2 !important;
    padding: 16px !important;
    border-radius: 8px !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        border-color: #feb2b2;
        box-shadow: 0 0 0 0 rgba(245, 101, 101, 0.4);
    }
    50% {
        border-color: #f56565;
        box-shadow: 0 0 0 8px rgba(245, 101, 101, 0.1);
    }
    100% {
        border-color: #feb2b2;
        box-shadow: 0 0 0 0 rgba(245, 101, 101, 0.4);
    }
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 12px;
}

.forgot-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Button Styles */
.btn-auth {
    width: 100%;
    padding: 14px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 6px;
    background: var(--auth-bg-gradient);
    border: none;
    color: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-auth:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-auth:active {
    transform: translateY(0);
}

.btn-auth:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-loader {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
}

.btn-auth.loading .btn-text {
    opacity: 0.7;
}

.btn-auth.loading .btn-loader {
    display: block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.form-footer p {
    color: #718096;
    font-size: 14px;
    margin: 0;
}

.form-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Message Styles */
.message {
    width: 100%;
    max-width: 420px;
    margin-top: 16px;
    padding: 14px 18px;
    border-radius: 10px;
    font-weight: 500;
    display: none;
    align-items: center;
    gap: 8px;
    animation: slideDown 0.3s ease;
    line-height: 1.4;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.success {
    background-color: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.message.success::before {
    content: '✅';
}

.message.error {
    background-color: #fed7d7;
    color: #742a2a;
    border: 1px solid #feb2b2;
}

.message.error::before {
    content: '❌';
}

.message.info {
    background-color: #bee3f8;
    color: #2a4365;
    border: 1px solid #90cdf4;
}

.message.info::before {
    content: 'ℹ️';
}

/* Security Notice */
.security-notice {
    margin-top: 32px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 16px;
    color: white;
}

.security-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.security-text strong {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
}

.security-text p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.4;
}

/* Mobile Responsiveness */
@media (max-width: 576px) {
    .auth-container {
        padding: 70px 16px 16px;
    }
    
    .auth-wrapper {
        max-width: 100%;
    }
    
    .auth-header h1 {
        font-size: 2rem;
    }
    
    .auth-subtitle {
        font-size: 0.9rem;
    }
    
    .auth-form {
        padding: 28px 20px;
    }
    
    .form-header {
        margin-bottom: 20px;
    }
    
    .form-header h2 {
        font-size: 1.5rem;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .tab-btn {
        padding: 12px 16px;
        font-size: 14px;
    }
    
    .tab-btn .tab-icon {
        display: none;
    }
    
    .form-options {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        margin-bottom: 20px;
    }
    
    .checkbox-wrapper {
        font-size: 13px;
    }
    
    .security-notice {
        flex-direction: column;
        text-align: center;
        margin-top: 20px;
        padding: 14px;
    }
    
    .security-text {
        text-align: center;
    }
    
    .public-nav .nav-links {
        gap: 4px;
    }
    
    .public-nav .nav-link {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .public-nav .nav-brand h2 {
        font-size: 1.3rem;
    }
}

@media (max-width: 400px) {
    .logo-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .auth-form {
        padding: 20px 16px;
    }
    
    .form-group {
        margin-bottom: 14px;
    }
    
    .form-header {
        margin-bottom: 16px;
    }
    
    .auth-form input {
        padding: 12px 14px 12px 42px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .btn-auth {
        padding: 12px;
    }
    
    .form-footer {
        margin-top: 16px;
        padding-top: 16px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .auth-form input {
        border-width: 3px;
    }
    
    .btn-auth {
        border: 2px solid #333;
    }
    
    .message {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .auth-form.active {
        animation: none;
    }
    
    .btn-auth:hover {
        transform: none;
    }
    
    .message {
        animation: none;
    }
}

