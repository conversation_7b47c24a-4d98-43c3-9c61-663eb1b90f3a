// FAQ page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeFAQ();
    initializeSearch();
    initializeFilters();
});

// FAQ functionality
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', function() {
            // Close other items if they're open
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });
            
            // Toggle current item
            item.classList.toggle('active');
        });
    });
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('faqSearch');
    if (!searchInput) return;
    
    const debouncedSearch = debounce(performSearch, 300);
    
    searchInput.addEventListener('input', function() {
        debouncedSearch(this.value.toLowerCase());
    });
}

function performSearch(searchTerm) {
    const faqItems = document.querySelectorAll('.faq-item');
    const faqSections = document.querySelectorAll('.faq-section');
    let hasResults = false;
    
    // Clear previous highlights
    clearHighlights();
    
    if (searchTerm === '') {
        // Show all items and sections
        faqItems.forEach(item => {
            item.classList.remove('hidden');
        });
        faqSections.forEach(section => {
            section.classList.remove('hidden');
        });
        removeNoResultsMessage();
        return;
    }
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question h3');
        const answer = item.querySelector('.faq-answer');
        
        const questionText = question.textContent.toLowerCase();
        const answerText = answer.textContent.toLowerCase();
        
        if (questionText.includes(searchTerm) || answerText.includes(searchTerm)) {
            item.classList.remove('hidden');
            hasResults = true;
            
            // Highlight search terms
            highlightText(question, searchTerm);
            highlightText(answer, searchTerm);
        } else {
            item.classList.add('hidden');
        }
    });
    
    // Show/hide sections based on visible items
    faqSections.forEach(section => {
        const visibleItems = section.querySelectorAll('.faq-item:not(.hidden)');
        if (visibleItems.length > 0) {
            section.classList.remove('hidden');
        } else {
            section.classList.add('hidden');
        }
    });
    
    // Show no results message if needed
    if (!hasResults) {
        showNoResultsMessage(searchTerm);
    } else {
        removeNoResultsMessage();
    }
}

function highlightText(element, searchTerm) {
    if (!searchTerm) return;
    
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
        const text = textNode.textContent;
        const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
        
        if (regex.test(text)) {
            const highlightedHTML = text.replace(regex, '<span class="highlight">$1</span>');
            const wrapper = document.createElement('div');
            wrapper.innerHTML = highlightedHTML;
            
            while (wrapper.firstChild) {
                textNode.parentNode.insertBefore(wrapper.firstChild, textNode);
            }
            textNode.parentNode.removeChild(textNode);
        }
    });
}

function clearHighlights() {
    const highlights = document.querySelectorAll('.highlight');
    highlights.forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function showNoResultsMessage(searchTerm) {
    removeNoResultsMessage();
    
    const noResults = document.createElement('div');
    noResults.className = 'no-results';
    noResults.id = 'noResults';
    noResults.innerHTML = `
        <h3>No results found</h3>
        <p>We couldn't find any FAQs matching "${escapeHtml(searchTerm)}"</p>
        <button class="btn btn-primary" onclick="clearSearch()">Clear Search</button>
    `;
    
    const faqGrid = document.querySelector('.faq-grid');
    faqGrid.appendChild(noResults);
}

function removeNoResultsMessage() {
    const noResults = document.getElementById('noResults');
    if (noResults) {
        noResults.remove();
    }
}

function clearSearch() {
    const searchInput = document.getElementById('faqSearch');
    if (searchInput) {
        searchInput.value = '';
        performSearch('');
    }
}

// Category filters
function initializeFilters() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    
    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter content
            const category = this.dataset.category;
            filterByCategory(category);
        });
    });
}

function filterByCategory(category) {
    const faqSections = document.querySelectorAll('.faq-section');
    
    if (category === 'all') {
        faqSections.forEach(section => {
            section.classList.remove('hidden');
        });
    } else {
        faqSections.forEach(section => {
            if (section.dataset.category === category) {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
        });
    }
    
    // Clear search when filtering
    const searchInput = document.getElementById('faqSearch');
    if (searchInput) {
        searchInput.value = '';
    }
    clearHighlights();
    removeNoResultsMessage();
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Analytics tracking (if needed)
function trackFAQInteraction(action, category, question) {
    // Add analytics tracking here if needed
    console.log('FAQ Interaction:', { action, category, question });
}

// Track FAQ question clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('.faq-question')) {
        const faqItem = e.target.closest('.faq-item');
        const section = faqItem.closest('.faq-section');
        const question = faqItem.querySelector('.faq-question h3').textContent;
        const category = section.dataset.category || 'unknown';
        
        trackFAQInteraction('question_click', category, question.substring(0, 50));
    }
});

// Keyboard navigation for accessibility
document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
        if (e.target.classList.contains('faq-question') || e.target.closest('.faq-question')) {
            e.preventDefault();
            const faqQuestion = e.target.classList.contains('faq-question') ? e.target : e.target.closest('.faq-question');
            faqQuestion.click();
        }
    }
});

// Make FAQ questions focusable for keyboard navigation
document.querySelectorAll('.faq-question').forEach(question => {
    question.setAttribute('tabindex', '0');
    question.setAttribute('role', 'button');
    question.setAttribute('aria-expanded', 'false');
    
    question.addEventListener('click', function() {
        const isActive = this.closest('.faq-item').classList.contains('active');
        this.setAttribute('aria-expanded', isActive ? 'true' : 'false');
    });
});
