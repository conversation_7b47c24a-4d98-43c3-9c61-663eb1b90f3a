/* Public pages mobile-first styles with Dark Mode Support */

/* CSS Variables for public pages */
:root {
    /* Light mode public colors */
    --public-nav-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --public-nav-text: white;
    --public-nav-text-muted: rgba(255, 255, 255, 0.9);
    --public-nav-hover: rgba(255, 255, 255, 0.1);
    --public-shadow: rgba(0, 0, 0, 0.1);
}

/* Dark mode public colors */
@media (prefers-color-scheme: dark) {
    :root {
        --public-nav-gradient: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        --public-nav-text: #f7fafc;
        --public-nav-text-muted: rgba(255, 255, 255, 0.8);
        --public-nav-hover: rgba(255, 255, 255, 0.1);
        --public-shadow: rgba(0, 0, 0, 0.2);
    }
}

/* Navigation */
.navbar {
    background: var(--public-nav-gradient);
    padding: 0;
    box-shadow: 0 2px 20px var(--public-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 70px;
}

.nav-brand h2 {
    color: var(--public-nav-text);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.nav-link {
    color: var(--public-nav-text-muted);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--public-nav-text);
    background: var(--public-nav-hover);
}

.nav-btn {
    margin-left: 8px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 8px;
}

.nav-toggle span {
    width: 20px;
    height: 2px;
    background: var(--public-nav-text);
    margin: 2px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: var(--public-nav-gradient);
    color: white;
    padding: 80px 0 60px;
    min-height: 90vh;
    display: flex;
    align-items: center;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
    align-items: center;
}

.hero-text {
    text-align: center;
}

.hero-text h1 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 16px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 32px;
    opacity: 0.9;
    line-height: 1.5;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 500;
}

.feature-icon {
    font-size: 1.2rem;
}

.hero-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-large {
    padding: 16px 32px;
    font-size: 16px;
    min-height: 52px;
}

.hero-visual {
    display: flex;
    justify-content: center;
}

.crypto-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.crypto-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    min-width: 200px;
    transition: transform 0.3s ease;
}

.crypto-card:hover {
    transform: translateY(-5px);
}

.crypto-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
}

.crypto-info h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.crypto-info p {
    margin: 4px 0 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 48px;
}

.section-header h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: #2c3e50;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f8f9fa;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
    max-width: 1000px;
    margin: 0 auto;
}

.feature-card {
    background: white;
    padding: 32px 24px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.feature-icon-large {
    font-size: 3rem;
    margin-bottom: 16px;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Investment Plans */
.investment-preview {
    padding: 80px 0;
    background: white;
}

.plans-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    max-width: 1000px;
    margin: 0 auto 40px;
}

.plan-card {
    background: white;
    border-radius: 16px;
    padding: 32px 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.plan-card.popular {
    border-color: #007bff;
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: 16px;
    right: -30px;
    background: #007bff;
    color: white;
    padding: 4px 40px;
    font-size: 0.8rem;
    font-weight: 600;
    transform: rotate(45deg);
}

.plan-header {
    text-align: center;
    margin-bottom: 24px;
}

.plan-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #2c3e50;
}

.plan-return {
    font-size: 2rem;
    font-weight: 800;
    color: #28a745;
    margin-bottom: 8px;
}

.plan-details {
    margin-bottom: 24px;
}

.plan-detail {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.plan-detail:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
    font-weight: 500;
}

.detail-value {
    font-weight: 600;
    color: #2c3e50;
}

.plan-features {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature {
    color: #28a745;
    font-weight: 500;
}

.section-action {
    text-align: center;
}

/* Statistics */
.stats {
    padding: 60px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
    max-width: 800px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 8px;
    color: #fff;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* How It Works */
.how-it-works {
    padding: 80px 0;
    background: #f8f9fa;
}

.steps-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
    max-width: 800px;
    margin: 0 auto;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    text-align: left;
}

.step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.step-content p {
    color: #666;
    line-height: 1.6;
}

/* Newsletter Section */
.newsletter {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.newsletter-text h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: #2c3e50;
}

.newsletter-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

.newsletter-form {
    width: 100%;
}

.newsletter-input-group {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.newsletter-input {
    flex: 1;
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    min-width: 250px;
}

.newsletter-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.newsletter-disclaimer {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

/* Call to Action */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 32px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
    margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 16px;
    font-weight: 600;
}

.footer-section h3 {
    font-size: 1.5rem;
    color: #3498db;
}

.footer-section p {
    opacity: 0.8;
    line-height: 1.6;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin: 8px 0;
    opacity: 0.7;
}

/* Legal Pages Styles */
.legal-container {
    padding: 100px 0 60px;
    background: #f8f9fa;
    min-height: 100vh;
}

.legal-header {
    text-align: center;
    margin-bottom: 50px;
}

.legal-header h1 {
    color: #2d3748;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 12px;
}

.last-updated {
    color: #718096;
    font-size: 1rem;
    font-style: italic;
}

.legal-content {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    padding: 50px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.legal-section {
    margin-bottom: 40px;
}

.legal-section:last-child {
    margin-bottom: 0;
}

.legal-section h2 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
}

.legal-section h3 {
    color: #4a5568;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 24px 0 12px 0;
}

.legal-section p {
    color: #4a5568;
    line-height: 1.7;
    margin-bottom: 16px;
}

.legal-section ul {
    color: #4a5568;
    line-height: 1.6;
    margin: 16px 0;
    padding-left: 20px;
}

.legal-section li {
    margin-bottom: 8px;
}

.legal-section li strong {
    color: #2d3748;
    font-weight: 600;
}

.legal-section a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.legal-section a:hover {
    text-decoration: underline;
    color: #5a6fd8;
}

/* Risk Warning Box */
.risk-warning {
    background: linear-gradient(135deg, #fed7d7 0%, #feebc8 100%);
    border: 1px solid #feb2b2;
    border-radius: 12px;
    padding: 24px;
    margin: 24px 0;
    position: relative;
}

.risk-warning h3 {
    color: #c53030;
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.risk-warning p {
    color: #742a2a;
    margin-bottom: 12px;
}

.risk-warning ul {
    color: #742a2a;
}

.risk-warning strong {
    color: #c53030;
}

/* Privacy Summary Box */
.privacy-summary {
    background: linear-gradient(135deg, #c6f6d5 0%, #bee3f8 100%);
    border: 1px solid #9ae6b4;
    border-radius: 12px;
    padding: 24px;
    margin-top: 32px;
}

.privacy-summary h3 {
    color: #22543d;
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.privacy-summary p {
    color: #22543d;
    margin: 0;
    font-weight: 500;
}

/* Legal Footer */
.legal-footer {
    margin-top: 40px;
    padding-top: 32px;
    border-top: 2px solid #e2e8f0;
    text-align: center;
}

.legal-footer p {
    color: #2d3748;
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--public-nav-gradient);
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        padding: 20px;
        transition: left 0.3s ease;
        gap: 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-link {
        padding: 16px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 0;
        text-align: left;
    }
    
    .nav-btn {
        margin: 16px 0 0 0;
        align-self: flex-start;
    }
    
    /* Toggle animation */
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-4px, 4px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-4px, -4px);
    }
    
    .legal-container {
        padding: 80px 0 40px;
    }
    
    .legal-header h1 {
        font-size: 2rem;
    }
    
    .legal-content {
        padding: 30px 20px;
        margin: 0 20px;
    }
    
    .legal-section h2 {
        font-size: 1.3rem;
    }
    
    .legal-section h3 {
        font-size: 1.1rem;
    }
    
    .risk-warning,
    .privacy-summary {
        padding: 20px 16px;
    }
}

@media (max-width: 480px) {
    .legal-content {
        margin: 0 16px;
        padding: 24px 16px;
    }
    
    .legal-header {
        margin-bottom: 30px;
    }
    
    .legal-header h1 {
        font-size: 1.8rem;
    }
    
    .legal-section {
        margin-bottom: 30px;
    }
    
    .legal-section h2 {
        font-size: 1.2rem;
    }
}

/* Tablet Styles */
@media (min-width: 577px) {
    .hero-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }
    
    .hero-text {
        text-align: left;
    }
    
    .hero-features,
    .hero-actions {
        justify-content: flex-start;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .plans-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .newsletter-content {
        grid-template-columns: 1fr 1fr;
        text-align: left;
    }
    
    .newsletter-form {
        justify-self: end;
    }
}

/* Mobile Styles */
@media (max-width: 576px) {
    .newsletter-text h2 {
        font-size: 1.8rem;
    }
    
    .newsletter-input-group {
        flex-direction: column;
        gap: 16px;
    }
    
    .newsletter-input {
        min-width: auto;
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .hero-text h1 {
        font-size: 3.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .plans-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .steps-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;
    }
    
    .step-item {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .hero-text h1 {
        font-size: 4rem;
    }
    
    .container {
        padding: 0 40px;
    }
}

/* Animation for elements coming into view */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card,
.plan-card,
.step-item {
    animation: fadeInUp 0.6s ease forwards;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .nav-link:hover,
    .nav-link.active {
        background: rgba(255, 255, 255, 0.3);
    }
    
    .feature-card,
    .plan-card {
        border: 2px solid #333;
    }
}
