// Deposit Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    initDepositPage();
});

let walletData = null;
let currentDepositAmount = 0;
let systemConfig = null; // Will store system configuration

// Track if a submission is in progress to prevent multiple clicks
let isSubmitting = false;
let lastSubmissionTime = 0;
const SUBMISSION_DEBOUNCE_TIME = 2000; // 2 seconds minimum between submissions

let balanceMonitorInterval = null;
let initialBalance = null;
let currentDepositAddress = null;

// Balance monitoring configuration
const BALANCE_CHECK_INTERVAL = 120000; // 2 minutes (120 seconds)
const MAX_MONITORING_TIME = 1800000; // 30 minutes

// Payment monitoring state
let isPaymentMonitoringActive = false;
let pageLeaveWarningEnabled = false;

function initDepositPage() {
    console.log('initDepositPage: Starting initialization...');
    
    try {
        console.log('initDepositPage: Loading system configuration...');
        loadSystemConfiguration();
        
        console.log('initDepositPage: Setting up deposit actions...');
        setupDepositActions();
        
        console.log('initDepositPage: Loading recent deposits...');
        loadRecentDeposits();
        
        console.log('initDepositPage: Initializing footer menu...');
        initFooterMenu();
        
        console.log('initDepositPage: Setting up deposit form...');
        setupDepositForm();
        
        console.log('initDepositPage: Setting up page leave warning...');
        setupPageLeaveWarning();
        
        console.log('initDepositPage: Initialization completed successfully');
    } catch (error) {
        console.error('initDepositPage: Error during initialization:', error);
        // Even if other functions fail, ensure the form is set up
        try {
            console.log('initDepositPage: Attempting fallback form setup...');
            setupDepositForm();
        } catch (formError) {
            console.error('initDepositPage: Failed to set up form even in fallback:', formError);
        }
    }
}

/**
 * Load system configuration including deposit limits
 */
async function loadSystemConfiguration() {
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'get_system_configuration'
            })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                systemConfig = result.data;
                console.log('System configuration loaded:', systemConfig);
                
                // Update deposit form with dynamic minimum
                updateDepositFormLimits();
            } else {
                console.error('Failed to load system configuration:', result.message);
            }
        }
    } catch (error) {
        console.error('Error loading system configuration:', error);
    }
}

/**
 * Update deposit form with dynamic limits
 */
function updateDepositFormLimits() {
    if (!systemConfig) return;
    
    const minAmount = systemConfig.deposit?.minimum_amount || 1;
    const maxAmount = systemConfig.deposit?.maximum_amount || 1000000;
    
    // Update input attributes
    const amountInput = document.getElementById('depositAmount');
    if (amountInput) {
        amountInput.min = minAmount;
        amountInput.max = maxAmount;
        amountInput.placeholder = `Enter amount (minimum ${securityUtils.escapeHtml(minAmount)} USDT)`;
    }
      // Update help text
    const helpText = document.querySelector('.form-help');
    if (helpText) {
        helpText.innerHTML = `<span class="help-icon">ℹ️</span> Minimum deposit: ${securityUtils.escapeHtml(minAmount)} USDT • Maximum: ${securityUtils.escapeHtml(maxAmount.toLocaleString())} USDT`;
    }
}

/**
 * Get the minimum deposit amount from system configuration
 */
function getMinimumDepositAmount() {
    return systemConfig?.deposit?.minimum_amount || 1;
}

/**
 * Load wallet data for deposit functionality
 */
async function loadDepositData() {
    try {
        const response = await apiCall('get_wallet');
        if (response.success && response.wallet) {
            walletData = response.wallet;
            updateDepositDisplay();
        } else {
            // No wallet exists - use null-safe element access
            const paymentAddress = document.getElementById('paymentAddress');
            const copyPaymentBtn = document.getElementById('copyPaymentBtn');
            const paymentQR = document.getElementById('paymentQR');
            
            if (paymentAddress) paymentAddress.textContent = 'Create a wallet first';
            if (copyPaymentBtn) copyPaymentBtn.style.display = 'none';
            if (paymentQR) paymentQR.style.display = 'none';
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
        const paymentAddress = document.getElementById('paymentAddress');
        if (paymentAddress) paymentAddress.textContent = 'Error loading wallet';
    }
}

/**
 * Update deposit display with wallet information
 */
function updateDepositDisplay() {
    if (walletData && walletData.address) {
        const paymentAddress = document.getElementById('paymentAddress');
        const copyPaymentBtn = document.getElementById('copyPaymentBtn');
        
        if (paymentAddress) paymentAddress.textContent = walletData.address;
        if (copyPaymentBtn) copyPaymentBtn.style.display = 'inline-block';
        
        // Generate QR code for payment
        generatePaymentQRCode(walletData.address);
    }
}

/**
 * Generate QR code for payment
 */
function generatePaymentQRCode(address, amount = null) {
    const qrContainer = document.getElementById('paymentQR');
    if (!qrContainer || !address) return;
    
    try {
        // Clear previous QR code
        qrContainer.innerHTML = '';
          // Create QR code data
        let qrData = address;
        if (amount && amount > 0) {
            // For TRON USDT deposits, we can include amount in QR
            qrData = `tron:${securityUtils.escapeHtml(address)}?amount=${securityUtils.escapeHtml(amount)}&token=USDT`;
        }
  
        
        // Use the QRCodeGenerator class
        if (typeof QRCodeGenerator !== 'undefined') {
            const qrGenerator = new QRCodeGenerator();
            const qrUrl = qrGenerator.generateQRCode(qrData, 220);
              qrContainer.innerHTML = `
                <div class="tron-qr-container">
                    <div class="qr-header">
                        <h5>📱 Scan with TRON Wallet</h5>
                        <small>USDT (TRC20) Network</small>
                    </div>
                    <img src="${securityUtils.escapeHtml(qrUrl)}" alt="Payment QR Code" class="tron-qr-image" style="max-width: 220px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);" />
                    <div class="qr-details">
                        ${amount ? `<div class="qr-amount-large">${securityUtils.escapeHtml(amount.toFixed(6))} USDT</div>` : ''}
                        <div class="qr-address-small">${securityUtils.escapeHtml(address.substring(0, 10))}...${securityUtils.escapeHtml(address.substring(address.length - 10))}</div>
                    </div>
                    <div class="qr-actions">
                        <button class="btn btn-sm btn-outline" onclick="copyPaymentAddress()">📋 Copy Address</button>
                        <button class="btn btn-sm btn-outline" onclick="downloadQRCode('${securityUtils.escapeHtml(qrUrl)}', 'deposit-qr-${Date.now()}')">💾 Download QR</button>
                    </div>
                </div>
            `;
        } else {            // Fallback: show address text if QR library not available
            qrContainer.innerHTML = `<div class="qr-fallback">
                <p>QR Code: ${securityUtils.escapeHtml(address)}</p>
                <small>QR Code generator not available</small>
            </div>`;
        }
    } catch (error) {
        console.warn('QR Code generation failed:', error);
        qrContainer.innerHTML = `<div class="qr-error">QR Code generation failed</div>`;
    }
}

/**
 * Load recent deposits for display
 */
async function loadRecentDeposits() {
    const recentDepositsList = document.getElementById('recentDepositsList');
    
    if (!recentDepositsList) return;
    
    recentDepositsList.innerHTML = '<div class="loading">Loading recent deposits...</div>';
    
    try {
        const response = await apiCall('get_transactions', {
            type: 'deposit',
            limit: 5,
            page: 0
        });
        
        if (response.success && response.transactions) {
            displayRecentDeposits(response.transactions);
        } else {
            recentDepositsList.innerHTML = '<div class="no-data">No deposits found</div>';
        }
    } catch (error) {
        console.error('Error loading recent deposits:', error);
        recentDepositsList.innerHTML = '<div class="no-data">Error loading deposits</div>';
    }
}

/**
 * Display recent deposits in the UI
 */
function displayRecentDeposits(deposits) {
    const recentDepositsList = document.getElementById('recentDepositsList');
    
    if (!deposits || deposits.length === 0) {
        recentDepositsList.innerHTML = '<div class="no-data">No recent deposits</div>';
        return;
    }
      const depositsHTML = deposits.map(deposit => `
        <div class="transaction-item deposit-item">
            <div class="transaction-header">
                <span class="transaction-type deposit">Deposit</span>
                <span class="transaction-status ${securityUtils.escapeHtml(deposit.status)}">${securityUtils.escapeHtml(formatStatus(deposit.status))}</span>
            </div>
            <div class="transaction-details">
                <div class="transaction-amount positive">
                    +${securityUtils.escapeHtml(deposit.amount || '0.00')} USDT
                </div>
                <div class="transaction-info">
                    <div class="transaction-hash">
                        <span class="label">Hash:</span>
                        <span class="hash">${securityUtils.escapeHtml(deposit.transaction_hash || 'Pending')}</span>
                    </div>
                    <div class="transaction-date">
                        <span class="label">Date:</span>
                        <span class="date">${securityUtils.escapeHtml(formatDate(deposit.created_at))}</span>
                    </div>
                    ${deposit.from_address ? `
                    <div class="transaction-address">
                        <span class="label">From:</span>
                        <span class="address">${securityUtils.escapeHtml(deposit.from_address)}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    recentDepositsList.innerHTML = depositsHTML;
}

/**
 * Format transaction status for display
 */
function formatStatus(status) {
    switch (status) {
        case 'confirmed':
            return 'Confirmed';
        case 'pending':
            return 'Pending';
        case 'failed':
            return 'Failed';
        default:
            return status || 'Unknown';
    }
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    if (!dateString) return 'Unknown';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (error) {
        return dateString;
    }
}

/**
 * Update process indicator
 */
function updateProcessIndicator(step) {
    const steps = document.querySelectorAll('.process-step');
    
    steps.forEach((stepEl, index) => {
        // Clear all classes first
        stepEl.classList.remove('active', 'completed', 'inactive');
        
        if (index + 1 < step) {
            // Previous steps are completed
            stepEl.classList.add('completed');
        } else if (index + 1 === step) {
            // Current step is active
            stepEl.classList.add('active');
        } else {
            // Future steps are inactive
            stepEl.classList.add('inactive');
        }
    });
}

function setupDepositActions() {
    const copyPaymentBtn = document.getElementById('copyPaymentBtn');
    const newDepositBtn = document.getElementById('newDepositBtn');
    const depositForm = document.getElementById('depositForm');
    
    if (copyPaymentBtn) {
        copyPaymentBtn.addEventListener('click', copyPaymentAddress);
    }
    
    if (newDepositBtn) {
        newDepositBtn.addEventListener('click', function() {
            // Hide payment details with animation
            const paymentDetails = document.getElementById('paymentDetails');
            if (paymentDetails) {
                paymentDetails.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                paymentDetails.style.opacity = '0';
                paymentDetails.style.transform = 'translateY(-20px)';
                
                setTimeout(() => {
                    paymentDetails.style.display = 'none';
                    paymentDetails.style.transform = 'translateY(20px)';
                }, 300);
            }
            
            // Reset form and focus on amount input
            if (depositForm) {
                depositForm.reset();
                const amountInput = document.getElementById('depositAmount');
                if (amountInput) {
                    setTimeout(() => {
                        amountInput.focus();
                        // Scroll form into view
                        depositForm.scrollIntoView({ 
                            behavior: 'smooth', 
                            block: 'center' 
                        });
                    }, 100);
                }
            }
            
            // Reset current deposit amount
            currentDepositAmount = 0;
            
            // Reset process indicator to step 1
            updateProcessIndicator(1);
            
            showNotification('Ready for new deposit', 'info');
        });
    }
    
    // Add form reset handler
    if (depositForm) {
        const resetBtn = depositForm.querySelector('button[type="reset"]');
        if (resetBtn) {
            resetBtn.addEventListener('click', function() {
                // Clear any error states
                const inputs = depositForm.querySelectorAll('input');
                inputs.forEach(input => {
                    input.classList.remove('error');
                    clearFieldError(input);
                });
                
                // Reset process indicator
                updateProcessIndicator(1);
                
                // Reset current deposit amount
                currentDepositAmount = 0;
                
                // Focus on amount input
                const amountInput = document.getElementById('depositAmount');
                if (amountInput) {
                    setTimeout(() => amountInput.focus(), 100);
                }
                
                showNotification('Form cleared', 'info');
            });
        }
    }
}

function setupDepositForm() {
    console.log('setupDepositForm: Starting form setup...');
    
    const depositForm = document.getElementById('depositForm');
    const amountInput = document.getElementById('depositAmount');
    
    console.log('setupDepositForm: depositForm element:', depositForm);
    console.log('setupDepositForm: amountInput element:', amountInput);
    
    if (depositForm) {
        console.log('setupDepositForm: Adding submit event listener...');
        depositForm.addEventListener('submit', handleDepositSubmit);
        
        // Prevent form submission through keyboard (Enter key) while processing
        depositForm.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && isSubmitting) {
                event.preventDefault();
                showNotification('Please wait, processing your request...', 'warning');
                return false;
            }
        });
        
        console.log('setupDepositForm: Submit event listener added successfully');
    } else {
        console.error('setupDepositForm: Could not find depositForm element!');
    }
    
    // Add real-time validation
    if (amountInput) {
        console.log('setupDepositForm: Setting up amount input validation...');
        // Only clear error state on focus - no validation on blur
        amountInput.addEventListener('focus', function() {
            this.classList.remove('error');
            clearFieldError(this);
        });
        
        // Format number on input
        amountInput.addEventListener('input', function() {
            // Remove any non-numeric characters except decimal point
            let value = this.value.replace(/[^0-9.]/g, '');
            
            // Ensure only one decimal point
            const decimalCount = (value.match(/\./g) || []).length;
            if (decimalCount > 1) {
                value = value.substring(0, value.lastIndexOf('.'));
            }
            
            this.value = value;
            
            // Clear error state when typing
            this.classList.remove('error');
            clearFieldError(this);
        });
        
        console.log('setupDepositForm: Amount input validation set up successfully');
    } else {
        console.error('setupDepositForm: Could not find amountInput element!');
    }
    
    console.log('setupDepositForm: Form setup completed');
}

function validateDepositAmount(input) {
    const value = parseFloat(input.value);
    const errorContainer = getOrCreateErrorContainer(input);
    
    // Clear previous errors
    clearFieldError(input);
    
    if (!input.value.trim()) {
        showFieldError(input, 'Please enter a deposit amount');
        return false;
    }
    
    if (isNaN(value) || value <= 0) {
        showFieldError(input, 'Please enter a valid amount');
        return false;    }
      if (value < getMinimumDepositAmount()) {
        showFieldError(input, `Minimum deposit amount is ${getMinimumDepositAmount()} USDT`);
        return false;
    }
    
    if (value > (systemConfig?.deposit?.maximum_amount || 1000000)) {
        const maxAmount = systemConfig?.deposit?.maximum_amount || 1000000;
        showFieldError(input, `Maximum deposit amount is ${maxAmount.toLocaleString()} USDT`);
        return false;
    }
    
    // Don't show success feedback automatically - just return true for valid amounts
    return true;
}

function showFieldError(input, message) {
    input.classList.add('error');
    const errorContainer = getOrCreateErrorContainer(input);
    errorContainer.textContent = message;
    errorContainer.style.display = 'block';
    errorContainer.className = 'field-error error';
}

function showFieldSuccess(input, message) {
    input.classList.remove('error');
    const errorContainer = getOrCreateErrorContainer(input);
    errorContainer.textContent = message;
    errorContainer.style.display = 'block';
    errorContainer.className = 'field-error success';
}

function clearFieldError(input) {
    const errorContainer = getOrCreateErrorContainer(input);
    errorContainer.style.display = 'none';
    errorContainer.textContent = '';
}

function getOrCreateErrorContainer(input) {
    const formGroup = input.closest('.form-group');
    let errorContainer = formGroup.querySelector('.field-error');
    
    if (!errorContainer) {
        errorContainer = document.createElement('div');
        errorContainer.className = 'field-error';
        formGroup.appendChild(errorContainer);
    }
    
    return errorContainer;
}

// Enhanced submission handler with debounce
async function handleDepositSubmit(event) {
    console.log('handleDepositSubmit: Form submission triggered!');
    console.log('handleDepositSubmit: Event:', event);
    
    event.preventDefault();
    
    console.log('handleDepositSubmit: Default form submission prevented');
    
    // Immediate check - prevent any processing if already submitting
    if (isSubmitting) {
        console.log('handleDepositSubmit: Already submitting, showing warning');
        showNotification('Please wait, processing your request...', 'warning');
        return false;
    }
    
    // Check debounce timing
    const now = Date.now();
    const timeSinceLastSubmission = now - lastSubmissionTime;
    
    if (timeSinceLastSubmission < SUBMISSION_DEBOUNCE_TIME && lastSubmissionTime > 0) {
        const remainingTime = Math.ceil((SUBMISSION_DEBOUNCE_TIME - timeSinceLastSubmission) / 1000);
        showNotification(`Please wait ${remainingTime} seconds before submitting again`, 'warning');
        return false;
    }
      const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Get form data and validate first
    const formData = new FormData(event.target);
    const amount = parseFloat(formData.get('amount'));
    const note = formData.get('note');    // Validate amount before proceeding
    if (!amount || amount < getMinimumDepositAmount()) {
        showNotification(`Please enter a valid amount (minimum ${getMinimumDepositAmount()} USDT)`, 'error');
        return false;
    }
    
    // Additional validation for reasonable amounts
    if (amount > 1000000) {
        showNotification('Amount too large. Please contact support for large deposits.', 'error');
        return false;
    }
      // Set submission flags immediately after all validation passes
    isSubmitting = true;
    lastSubmissionTime = now;
    
    try {
        
        // Show loading state with spinner
        submitBtn.innerHTML = '<span class="btn-spinner">⏳</span> Processing...';
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.7';
        submitBtn.style.cursor = 'not-allowed';
          // Also disable the form inputs to prevent any other interaction
        const form = event.target;
        const formInputs = form.querySelectorAll('input, button[type="reset"]');
        formInputs.forEach(input => {
            input.disabled = true;
            input.style.opacity = '0.6';
        });
        
        currentDepositAmount = amount;
        
        // Add visual feedback during processing
        showNotification('Generating payment details...', 'info');
        
        // Show payment details with smooth animation
        await showPaymentDetails(amount, note);
        
    } catch (error) {
        console.error('Error handling deposit submission:', error);
        showNotification('Error processing deposit request. Please try again.', 'error');    } finally {
        // Reset submission flag and restore button state
        isSubmitting = false;
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        submitBtn.style.opacity = '1';
        submitBtn.style.cursor = 'pointer';
        
        // Re-enable form inputs
        const form = event.target;
        const formInputs = form.querySelectorAll('input, button[type="reset"]');
        formInputs.forEach(input => {
            input.disabled = false;
            input.style.opacity = '1';
        });
    }
}

async function showPaymentDetails(amount, note) {
    try {
        // Update process indicator to step 2
        updateProcessIndicator(2);
        
        // Generate a unique wallet address for this specific deposit
        showNotification('Generating unique wallet address for this deposit...', 'info');
        
        try {
            const walletResponse = await apiCall('create_deposit_wallet', {
                amount: amount,
                note: note || ''
            });
            
            if (walletResponse.success) {
                // Use the newly created deposit-specific wallet
                walletData = {
                    address: walletResponse.deposit_address,
                    hex_address: walletResponse.hex_address
                };
                showNotification('Unique wallet address generated successfully!', 'success');            } else if (walletResponse.error && typeof walletResponse.error === 'string' && walletResponse.error.includes('already exists')) {
                // Wallet already exists, get the existing one
                showNotification('Loading existing wallet address...', 'info');
                const existingWalletResponse = await apiCall('get_wallet');
                if (existingWalletResponse.success && existingWalletResponse.wallet) {
                    walletData = existingWalletResponse.wallet;
                    showNotification('Wallet address loaded successfully!', 'success');
                } else {
                    throw new Error('Failed to load existing wallet');
                }            } else {
                // Handle different error response formats
                let errorMessage = 'Failed to generate wallet';
                if (walletResponse.error) {
                    if (typeof walletResponse.error === 'string') {
                        errorMessage = walletResponse.error;
                    } else if (typeof walletResponse.error === 'object' && walletResponse.error.message) {
                        errorMessage = walletResponse.error.message;
                    }
                }
                throw new Error(errorMessage);
            }
        } catch (walletError) {
            console.error('Wallet generation/loading error:', walletError);
            showNotification('Error loading wallet. Please try again.', 'error');
            updateProcessIndicator(1);
            return;
        }

        if (!walletData || !walletData.address) {
            showNotification('Unable to generate wallet address. Please try again.', 'error');
            updateProcessIndicator(1);
            return;
        }

        // Store current deposit details for monitoring
        currentDepositAddress = walletData.address;
        
        // Get initial balance before starting monitoring
        await getInitialBalance();
        
        // Update payment details with animation
        const paymentDetailsCard = document.getElementById('paymentDetails');
        
        // Update deposit amount display
        const amountDisplay = document.getElementById('depositAmountDisplay');
        if (amountDisplay) {            // Animate the amount change
            amountDisplay.style.opacity = '0.5';
            setTimeout(() => {
                amountDisplay.textContent = amount.toFixed(2);
                amountDisplay.style.opacity = '1';
            }, 200);
        }
        
        // Update exact amount warning
        const exactAmountWarning = document.getElementById('exactAmountWarning');
        if (exactAmountWarning) {
            exactAmountWarning.textContent = `${amount.toFixed(2)} USDT`;
        }
        
        // Handle deposit note
        const depositNoteDisplay = document.getElementById('depositNoteDisplay');
        const depositNoteValue = document.getElementById('depositNoteValue');
        if (note && note.trim()) {
            if (depositNoteValue) depositNoteValue.textContent = note;
            if (depositNoteDisplay) depositNoteDisplay.style.display = 'flex';
        } else {
            if (depositNoteDisplay) depositNoteDisplay.style.display = 'none';
        }
        
        // Set payment address with loading animation
        const paymentAddressElement = document.getElementById('paymentAddress');
        if (paymentAddressElement) {
            paymentAddressElement.textContent = 'Loading address...';
            paymentAddressElement.style.opacity = '0.5';
            setTimeout(() => {
                paymentAddressElement.textContent = walletData.address;
                paymentAddressElement.style.opacity = '1';
            }, 300);
        }        // Show payment details section with fade-in animation
        if (paymentDetailsCard) {
            paymentDetailsCard.style.display = 'block';
            paymentDetailsCard.style.opacity = '0';
            paymentDetailsCard.style.transform = 'translateY(20px)';
            
            // Trigger reflow
            paymentDetailsCard.offsetHeight;
            
            // Animate in
            paymentDetailsCard.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            paymentDetailsCard.style.opacity = '1';
            paymentDetailsCard.style.transform = 'translateY(0)';
              // Generate QR code AFTER the payment details are visible and animated
            setTimeout(() => {
                generatePaymentQRCode(walletData.address, amount);
            }, 600); // Longer delay to ensure animation is complete
        }
        
        // Scroll to payment details with delay to allow animation
        setTimeout(() => {
            paymentDetailsCard.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start',
                inline: 'nearest'
            });
        }, 200);
        
        // Update process indicator to step 3 after a delay
        setTimeout(() => {
            updateProcessIndicator(3);
            showNotification('Payment details ready! Complete the deposit using the instructions below.', 'success');
            
            // Start balance monitoring after payment details are shown
            startBalanceMonitoring(amount);
        }, 600);
        
    } catch (error) {
        console.error('Error showing payment details:', error);
        showNotification('Error generating payment details. Please try again.', 'error');
        // Reset process indicator on error
        updateProcessIndicator(1);
    }
}

/**
 * Get initial balance before monitoring starts
 */
async function getInitialBalance() {
    try {
        if (!currentDepositAddress) return;
        
        const response = await apiCall('get_live_wallet', {
            address: currentDepositAddress
        });
        
        if (response.success && typeof response.balance !== 'undefined') {
            initialBalance = parseFloat(response.balance) || 0;
            console.log('Initial balance recorded:', initialBalance);
        }
    } catch (error) {
        console.warn('Could not get initial balance:', error);
        initialBalance = 0;
    }
}

/**
 * Start automatic balance monitoring
 */
function startBalanceMonitoring(expectedAmount) {
    // Stop any existing monitoring
    stopBalanceMonitoring();
    
    if (!currentDepositAddress) {
        console.warn('No deposit address available for monitoring');
        return;
    }
    
    console.log(`Starting balance monitoring for ${expectedAmount} USDT deposit`);
    console.log(`Monitoring interval: ${BALANCE_CHECK_INTERVAL / 1000} seconds`);
    
    // Enable page leave warning when monitoring starts
    isPaymentMonitoringActive = true;
    enablePageLeaveWarning();
    
    // Show monitoring status
    showMonitoringStatus(true);
    
    let monitoringStartTime = Date.now();
    let checkCount = 0;
    
    // Perform initial balance check immediately
    checkBalanceChange(expectedAmount, checkCount++);
    
    balanceMonitorInterval = setInterval(async () => {
        try {
            // Check if monitoring has been running too long
            const elapsed = Date.now() - monitoringStartTime;
            if (elapsed > MAX_MONITORING_TIME) {
                console.log('Balance monitoring timed out after 30 minutes');
                stopBalanceMonitoring();
                showMonitoringStatus(false, 'Monitoring stopped after 30 minutes');
                return;
            }
            
            console.log(`Performing balance check #${checkCount} (${Math.round(elapsed/1000)}s elapsed)`);
            await checkBalanceChange(expectedAmount, checkCount++);
            
        } catch (error) {
            console.error('Error during balance monitoring:', error);
            // Don't stop monitoring on individual check errors
        }
    }, BALANCE_CHECK_INTERVAL);
    
    // Auto-stop monitoring after maximum time
    setTimeout(() => {
        if (balanceMonitorInterval) {
            stopBalanceMonitoring();
            showMonitoringStatus(false, 'Monitoring stopped automatically');
        }
    }, MAX_MONITORING_TIME);
}

async function checkBalanceChange(expectedAmount, checkNumber = 0) {
    try {
        console.log(`Balance check #${checkNumber}: Checking address ${currentDepositAddress?.substring(0, 10)}...`);
        
        const response = await apiCall('get_live_wallet', {
            address: currentDepositAddress
        });
        
        if (!response.success) {
            console.warn(`Balance check #${checkNumber} failed:`, response.error);
            // Update status to show failed check
            updateMonitoringStatusWithError(response.error);
            return;
        }
        
        const currentBalance = parseFloat(response.balance) || 0;
        const balanceIncrease = currentBalance - (initialBalance || 0);
        
        console.log(`Balance check #${checkNumber}: Initial: ${initialBalance}, Current: ${currentBalance}, Increase: ${balanceIncrease}, Expected: ${expectedAmount}`);
        
        // Update monitoring status with current balance info
        updateMonitoringStatusWithBalance(currentBalance, balanceIncrease, checkNumber);
        
        // Check if balance increased by expected amount (with tolerance)
        const tolerance = 0.001; // Allow small precision differences
        if (balanceIncrease >= (expectedAmount - tolerance)) {
            console.log(`Deposit detected in check #${checkNumber}! Balance increased by:`, balanceIncrease);
            
            // Stop monitoring and disable page leave warning
            stopBalanceMonitoring();
            
            // Show success notification
            showDepositSuccessAlert(balanceIncrease, currentBalance);
            
            // Refresh wallet data
            await loadDepositData();
            await loadRecentDeposits();
        }
        
    } catch (error) {
        console.error(`Error in balance check #${checkNumber}:`, error);
        updateMonitoringStatusWithError(error.message);
    }
}

function stopBalanceMonitoring() {
    if (balanceMonitorInterval) {
        clearInterval(balanceMonitorInterval);
        balanceMonitorInterval = null;
        console.log('Balance monitoring stopped');
    }
    
    // Disable page leave warning when monitoring stops
    isPaymentMonitoringActive = false;
    disablePageLeaveWarning();
    
    showMonitoringStatus(false);
}

function showMonitoringStatus(isActive, customMessage = null) {
    let statusElement = document.getElementById('balanceMonitoringStatus');
    
    if (!statusElement) {
        // Create status element if it doesn't exist
        statusElement = document.createElement('div');
        statusElement.id = 'balanceMonitoringStatus';
        statusElement.className = 'balance-monitoring-status';
        
        // Insert after payment details
        const paymentDetails = document.getElementById('paymentDetails');
        if (paymentDetails) {
            paymentDetails.appendChild(statusElement);
        }
    }
      if (isActive) {
        statusElement.innerHTML = `
            <div class="monitoring-indicator active">
                <div class="monitoring-pulse"></div>
                <div class="monitoring-text">
                    <strong>🔍 Monitoring for Payment</strong>
                    <p>Automatically checking your balance every 2 minutes. You'll be notified when your deposit is detected.</p>
                </div>
            </div>
        `;
        statusElement.style.display = 'block';
    } else if (customMessage) {
        statusElement.innerHTML = `
            <div class="monitoring-indicator stopped">
                <div class="monitoring-text">
                    <strong>⏹️ Monitoring Stopped</strong>
                    <p>${customMessage}</p>
                </div>
            </div>
        `;
        // Auto-hide after 5 seconds
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 5000);
    } else {
        statusElement.style.display = 'none';
    }
}

function showDepositSuccessAlert(amount, newBalance) {
    console.log('showDepositSuccessAlert called with:', { amount, newBalance });
    
    // Remove any existing success overlay
    const existingOverlay = document.getElementById('depositSuccessOverlay');
    if (existingOverlay) {
        existingOverlay.remove();
        console.log('Removed existing deposit success overlay');
    }
    
    // Create success modal/alert
    const alertHTML = `
        <div class="deposit-success-overlay" id="depositSuccessOverlay">
            <div class="deposit-success-modal">
                <div class="success-header">
                    <div class="success-icon">✅</div>
                    <h3>Deposit Received!</h3>
                </div>
                <div class="success-content">
                    <p><strong>Amount Detected:</strong> ${amount.toFixed(6)} USDT</p>
                    <p><strong>New Balance:</strong> ${newBalance.toFixed(6)} USDT</p>
                    <p>Your deposit has been automatically detected and will be credited to your account shortly.</p>
                </div>
                <div class="success-actions">
                    <button class="btn btn-primary" onclick="closeDepositSuccessAlert()">Continue</button>
                    <button class="btn btn-success" onclick="window.location.href='invest.php'">Invest Now</button>
                    <button class="btn btn-secondary" onclick="window.location.href='wallet.php'">View Wallet</button>
                </div>
            </div>
        </div>
    `;
    
    // Add to page
    document.body.insertAdjacentHTML('beforeend', alertHTML);
    console.log('Deposit success modal added to DOM');
    
    // Verify modal was added
    const addedModal = document.getElementById('depositSuccessOverlay');
    if (addedModal) {
        console.log('Modal successfully added and found in DOM');
    } else {
        console.error('Modal was not found in DOM after insertion');
    }
}

function closeDepositSuccessAlert() {
    console.log('closeDepositSuccessAlert called');
    const overlay = document.getElementById('depositSuccessOverlay');
    if (overlay) {
        overlay.remove();
        console.log('Deposit success modal closed and removed from DOM');
    } else {
        console.warn('No deposit success overlay found to close');
    }
}

// Copy payment address to clipboard
function copyPaymentAddress() {
    if (!walletData || !walletData.address) {
        showNotification('No payment address available to copy', 'error');
        return;
    }
    
    // Try modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(walletData.address).then(() => {
            showNotification('Payment address copied to clipboard!', 'success');
        }).catch(() => {
            // Fall back to legacy method
            fallbackCopyToClipboard(walletData.address);
        });
    } else {
        // Use fallback method for older browsers
        fallbackCopyToClipboard(walletData.address);
    }
}

/**
 * Fallback method to copy text to clipboard
 */
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('Payment address copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy address. Please copy manually.', 'error');
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showNotification('Copy failed. Please select and copy the address manually.', 'error');
    } finally {
        document.body.removeChild(textArea);
    }
}

/**
 * Download QR code image
 */
function downloadQRCode(qrUrl, filename) {
    try {
        const link = document.createElement('a');
        link.href = qrUrl;
        link.download = `${filename}.png`;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        showNotification('QR code download started', 'success');
    } catch (error) {        console.error('Download failed:', error);
        showNotification('Download failed. Opening QR code in new tab.', 'error');
        window.open(qrUrl, '_blank');
    }
}

// Clean up monitoring when leaving page
window.addEventListener('beforeunload', () => {
    stopBalanceMonitoring();
});

// Shared utility functions
async function apiCall(endpoint, data = null) {
    try {
        // Define state-changing operations that require CSRF protection
        const stateChangingActions = [
            'create_investment', 'withdraw', 'record_deposit', 'change_password',
            'create_deposit_wallet'
        ];
        
        // For state-changing operations, get CSRF token
        if (stateChangingActions.includes(endpoint)) {
            try {
                // Get CSRF token
                const csrfResponse = await fetch('../ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_csrf_token',
                        context: 'user'
                    })
                });
                
                if (!csrfResponse.ok) {
                    throw new Error(`Failed to get CSRF token: ${csrfResponse.status}`);
                }
                
                const csrfResult = await csrfResponse.json();
                if (!csrfResult.success || !csrfResult.csrf_token) {
                    throw new Error('Failed to obtain CSRF token');
                }
                
                // Make the actual request with CSRF token
                const url = `../ajax.php`;
                const options = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: endpoint,
                        csrf_token: csrfResult.csrf_token,
                        ...data
                    })
                };
                
                const response = await fetch(url, options);
                return await response.json();
                
            } catch (error) {
                console.error('CSRF protected API call failed:', error);
                showNotification('Security token error. Please refresh the page and try again.', 'error');
                throw error;
            }
        }
        
        // Regular API call for read-only operations
        const url = `../ajax.php`;
        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: endpoint,
                ...data
            })
        };
        
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${getNotificationIcon(type)}</span>
            <span class="notification-message">${message}</span>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => notification.classList.add('show'), 10);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return '✅';
        case 'error': return '❌';
        case 'warning': return '⚠️';
        case 'info': 
        default: return 'ℹ️';
    }
}

function setButtonLoading(button, loading, loadingText = 'Loading...') {
    if (!button) return;
    
    if (loading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = `<span class="btn-spinner">⏳</span> ${loadingText}`;
        button.disabled = true;
        button.style.opacity = '0.7';
    } else {
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.disabled = false;
        button.style.opacity = '1';
        delete button.dataset.originalText;
    }
}

function initFooterMenu() {
    // Footer menu initialization if needed
    const footerMenuItems = document.querySelectorAll('.footer-menu a');
    const currentPage = window.location.pathname.split('/').pop();
    
    footerMenuItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && href.includes(currentPage)) {
            item.classList.add('active');
        }
    });
}

/**
 * Set up page leave warning system
 */
function setupPageLeaveWarning() {
    // Add beforeunload event listener to warn users
    window.addEventListener('beforeunload', function(e) {
        if (pageLeaveWarningEnabled && isPaymentMonitoringActive) {
            const confirmationMessage = 'Your payment is being processed. Are you sure you want to leave this page? You may miss the payment confirmation.';
            
            // For modern browsers
            e.preventDefault();
            e.returnValue = confirmationMessage;
            
            // Show custom notification
            showNotification('⚠️ Please stay on this page until payment is confirmed!', 'warning');
            
            return confirmationMessage;
        }
    });
    
    // Add visibility change detection (for tab switching)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden && isPaymentMonitoringActive && pageLeaveWarningEnabled) {
            console.warn('User switched away from payment page during monitoring');
            showNotification('🚨 Please return to this tab - payment monitoring is active!', 'warning');
        }
    });
    
    // Add page focus/blur detection
    window.addEventListener('blur', function() {
        if (isPaymentMonitoringActive && pageLeaveWarningEnabled) {
            console.warn('User moved focus away from payment page');
        }
    });
    
    window.addEventListener('focus', function() {
        if (isPaymentMonitoringActive && pageLeaveWarningEnabled) {
            console.log('User returned focus to payment page');
            showNotification('✅ Welcome back! Payment monitoring is still active.', 'success');
        }
    });
}

/**
 * Enable page leave warning
 */
function enablePageLeaveWarning() {
    pageLeaveWarningEnabled = true;
    console.log('Page leave warning enabled');
    
    // Add visual indicator
    updatePageStatusIndicator(true);
}

/**
 * Disable page leave warning
 */
function disablePageLeaveWarning() {
    pageLeaveWarningEnabled = false;
    console.log('Page leave warning disabled');
    
    // Remove visual indicator
    updatePageStatusIndicator(false);
}

/**
 * Update page status indicator
 */
function updatePageStatusIndicator(isActive) {
    // Add or remove visual indicator in the page header
    let indicator = document.getElementById('paymentStatusIndicator');
    
    if (isActive) {
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'paymentStatusIndicator';
            indicator.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #dc3545;
                color: white;
                text-align: center;
                padding: 8px;
                font-weight: bold;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            `;
            indicator.innerHTML = '🚨 PAYMENT MONITORING ACTIVE - Do not leave this page!';
            document.body.appendChild(indicator);
            
            // Add some top padding to body to account for the indicator
            document.body.style.paddingTop = '50px';
        }
    } else {
        if (indicator) {
            indicator.remove();
            document.body.style.paddingTop = '';
        }
    }
}

// Add helper functions for enhanced monitoring status
function updateMonitoringStatusWithBalance(currentBalance, balanceIncrease, checkNumber) {
    const statusElement = document.getElementById('balanceMonitoringStatus');
    if (statusElement) {
        const activeIndicator = statusElement.querySelector('.monitoring-indicator.active');
        if (activeIndicator) {
            const monitoringText = activeIndicator.querySelector('.monitoring-text p');
            if (monitoringText) {
                monitoringText.innerHTML = `
                    Check #${checkNumber} complete. Current balance: ${currentBalance.toFixed(6)} USDT 
                    (increase: +${balanceIncrease.toFixed(6)} USDT). Next check in 2 minutes.
                `;
            }
        }
    }
}

function updateMonitoringStatusWithError(errorMessage) {
    const statusElement = document.getElementById('balanceMonitoringStatus');
    if (statusElement) {
        const activeIndicator = statusElement.querySelector('.monitoring-indicator.active');
        if (activeIndicator) {
            const monitoringText = activeIndicator.querySelector('.monitoring-text p');
            if (monitoringText) {
                monitoringText.innerHTML = `
                    Balance check failed: ${errorMessage}. Retrying in 2 minutes...
                `;
            }        }
    }
}

// Clean up monitoring when leaving page
window.addEventListener('beforeunload', () => {
    stopBalanceMonitoring();
});

// Note: apiCall function is defined earlier in the file with CSRF support

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${getNotificationIcon(type)}</span>
            <span class="notification-message">${message}</span>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => notification.classList.add('show'), 10);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return '✅';
        case 'error': return '❌';
        case 'warning': return '⚠️';
        case 'info': 
        default: return 'ℹ️';
    }
}

function setButtonLoading(button, loading, loadingText = 'Loading...') {
    if (!button) return;
    
    if (loading) {
        button.dataset.originalText = button.innerHTML;
        button.innerHTML = `<span class="btn-spinner">⏳</span> ${loadingText}`;
        button.disabled = true;
        button.style.opacity = '0.7';
    } else {
        button.innerHTML = button.dataset.originalText || button.innerHTML;
        button.disabled = false;
        button.style.opacity = '1';
        delete button.dataset.originalText;
    }
}

function initFooterMenu() {
    // Footer menu initialization if needed
    const footerMenuItems = document.querySelectorAll('.footer-menu a');
    const currentPage = window.location.pathname.split('/').pop();
    
    footerMenuItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && href.includes(currentPage)) {
            item.classList.add('active');
        }
    });
}

/**
 * Set up page leave warning system
 */
function setupPageLeaveWarning() {
    // Add beforeunload event listener to warn users
    window.addEventListener('beforeunload', function(e) {
        if (pageLeaveWarningEnabled && isPaymentMonitoringActive) {
            const confirmationMessage = 'Your payment is being processed. Are you sure you want to leave this page? You may miss the payment confirmation.';
            
            // For modern browsers
            e.preventDefault();
            e.returnValue = confirmationMessage;
            
            // Show custom notification
            showNotification('⚠️ Please stay on this page until payment is confirmed!', 'warning');
            
            return confirmationMessage;
        }
    });
    
    // Add visibility change detection (for tab switching)
    document.addEventListener('visibilitychange', function() {
        if (document.hidden && isPaymentMonitoringActive && pageLeaveWarningEnabled) {
            console.warn('User switched away from payment page during monitoring');
            showNotification('🚨 Please return to this tab - payment monitoring is active!', 'warning');
        }
    });
    
    // Add page focus/blur detection
    window.addEventListener('blur', function() {
        if (isPaymentMonitoringActive && pageLeaveWarningEnabled) {
            console.warn('User moved focus away from payment page');
        }
    });
    
    window.addEventListener('focus', function() {
        if (isPaymentMonitoringActive && pageLeaveWarningEnabled) {
            console.log('User returned focus to payment page');
            showNotification('✅ Welcome back! Payment monitoring is still active.', 'success');
        }
    });
}

// Failsafe form submission handler - added at the end to ensure it always works
document.addEventListener('DOMContentLoaded', function() {
    console.log('FAILSAFE: Adding backup form submission handler...');
    
    // Wait a bit to ensure all other scripts have loaded
    setTimeout(function() {
        const form = document.getElementById('depositForm');
        const submitBtn = form ? form.querySelector('button[type="submit"]') : null;
        
        if (form && submitBtn) {
            console.log('FAILSAFE: Found form elements, checking if primary handler is working...');
            
            // Check if form already has our event listener by testing submission
            let hasWorkingHandler = false;
            
            // Create a test to see if the form submission is already handled
            const testHandler = function(e) {
                hasWorkingHandler = true;
                console.log('FAILSAFE: Primary handler detected, removing backup');
                form.removeEventListener('submit', failsafeHandler);
            };
            
            // Add test handler with high priority (capture phase)
            form.addEventListener('submit', testHandler, true);
            
            // Backup handler in case primary fails
            const failsafeHandler = function(event) {
                console.log('FAILSAFE: Backup handler activated - primary handler may have failed');
                event.preventDefault();
                
                const amount = document.getElementById('depositAmount')?.value;
                const note = document.getElementById('note')?.value;
                
                console.log('FAILSAFE: Processing deposit with amount:', amount);
                
                if (!amount || parseFloat(amount) <= 0) {
                    alert('Please enter a valid amount');
                    return;
                }
                
                if (parseFloat(amount) < 1) {
                    alert('Minimum deposit amount is 1 USDT');
                    return;
                }
                
                // Show processing state
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '⏳ Processing...';
                submitBtn.disabled = true;
                
                // Call the main handler if it exists, otherwise use simplified flow
                if (typeof handleDepositSubmit === 'function') {
                    console.log('FAILSAFE: Calling main handleDepositSubmit function');
                    try {
                        handleDepositSubmit(event);
                    } catch (error) {
                        console.error('FAILSAFE: Main handler failed, using simplified flow:', error);
                        showSimplifiedPaymentFlow(amount, note, submitBtn, originalText);
                    }
                } else {
                    console.log('FAILSAFE: Main handler not found, using simplified flow');
                    showSimplifiedPaymentFlow(amount, note, submitBtn, originalText);
                }
            };
            
            // Add backup handler with lower priority
            form.addEventListener('submit', failsafeHandler, false);
            
            // Remove test handler after a short delay
            setTimeout(() => {
                form.removeEventListener('submit', testHandler, true);
                if (!hasWorkingHandler) {
                    console.log('FAILSAFE: Primary handler not detected, backup handler is active');
                }
            }, 100);
            
        } else {
            console.error('FAILSAFE: Could not find form elements');
        }
    }, 1000);
});

// Simplified payment flow for failsafe mode
async function showSimplifiedPaymentFlow(amount, note, submitBtn, originalText) {
    console.log('FAILSAFE: Starting simplified payment flow');
    
    try {
        // Step 1: Try to get or create wallet
        console.log('FAILSAFE: Attempting to get wallet...');
        
        let walletResponse = await fetch('../ajax.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action: 'get_wallet' })
        });
        
        let walletData = await walletResponse.json();
          // If no wallet exists, create a deposit-specific wallet
        if (!walletData.success || !walletData.wallet?.address) {
            console.log('FAILSAFE: No wallet found, creating unique deposit wallet...');
            
            try {
                // Use the new deposit wallet creation with CSRF token
                const csrfResponse = await fetch('../ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'get_csrf_token', context: 'user' })
                });
                
                const csrfResult = await csrfResponse.json();
                if (!csrfResult.success) {
                    throw new Error('Failed to get CSRF token');
                }
                
                walletResponse = await fetch('../ajax.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        action: 'create_deposit_wallet',
                        csrf_token: csrfResult.csrf_token,
                        amount: amount,
                        note: note || ''
                    })
                });
                
                walletData = await walletResponse.json();
                
                // Transform the response to match expected format
                if (walletData.success && walletData.deposit_address) {
                    walletData.address = walletData.deposit_address;
                }
            } catch (error) {
                console.error('FAILSAFE: Failed to create deposit wallet:', error);
                throw new Error('Failed to create unique deposit address');
            }
        }
          if (walletData.success && (walletData.wallet?.address || walletData.address || walletData.deposit_address)) {
            const address = walletData.wallet?.address || walletData.address || walletData.deposit_address;
            console.log('FAILSAFE: Wallet address obtained:', address.substring(0, 10) + '...');
            
            // Show payment details
            showSimplifiedPaymentDetails(address, amount, note);
            
        } else {
            throw new Error('Failed to get or create unique deposit address: ' + (walletData.error || 'Unknown error'));
        }
        
    } catch (error) {
        console.error('FAILSAFE: Error in simplified flow:', error);
        alert('Error processing deposit request. Please try again or contact support.');
    } finally {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

// Simplified payment details display
function showSimplifiedPaymentDetails(address, amount, note) {
    console.log('FAILSAFE: Displaying simplified payment details');
    
    // Hide the form
    const depositFormContainer = document.querySelector('.deposit-form')?.parentElement;
    if (depositFormContainer) {
        depositFormContainer.style.display = 'none';
    }
    
    // Show payment details
    const paymentSection = document.getElementById('paymentDetailsSection');
    if (paymentSection) {
        paymentSection.style.display = 'block';
        
        // Update payment details
        const paymentAddress = document.getElementById('paymentAddress');
        const paymentAmount = document.getElementById('paymentAmount');
        const exactAmountWarning = document.getElementById('exactAmountWarning');
        
        if (paymentAddress) paymentAddress.textContent = address;
        if (paymentAmount) paymentAmount.textContent = amount;
        if (exactAmountWarning) exactAmountWarning.textContent = `${amount} USDT`;
        
        // Generate QR code if function exists
        if (typeof generatePaymentQRCode === 'function') {
            generatePaymentQRCode(address, amount);
        }
        
        // Show success message
        if (typeof showNotification === 'function') {
            showNotification('Payment details ready! Send USDT to the address above.', 'success');
        } else {
            alert('Payment details ready! Send exactly ' + amount + ' USDT to the address shown.');
        }
        
        console.log('FAILSAFE: Payment details displayed successfully');
    } else {
        // Fallback: show address in alert
        alert(`Send exactly ${amount} USDT (TRC-20) to this address:\n\n${address}\n\nSave this address for your transaction.`);
        console.log('FAILSAFE: Used fallback alert for payment details');
    }
}

console.log('FAILSAFE: Backup form submission system loaded');

// Test function for debugging the deposit success modal
// Call this from browser console: testDepositSuccessModal()
window.testDepositSuccessModal = function(amount = 100.50, newBalance = 1234.567890) {
    console.log('Testing deposit success modal...');
    showDepositSuccessAlert(amount, newBalance);
};

// Global function to easily close modal from console
window.closeModal = function() {
    closeDepositSuccessAlert();
};
