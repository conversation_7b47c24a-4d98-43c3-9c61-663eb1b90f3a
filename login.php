<?php
// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
$isLoggedIn = SessionService::isAuthenticated();
$isAdmin = SessionService::isAdmin();

// Redirect to admin if admin user
if ($isLoggedIn && $isAdmin && !isset($_GET['user_view'])) {
    header('Location: admin.php');
    exit;
}

// Redirect to dashboard if logged in
if ($isLoggedIn && !isset($_GET['logout'])) {
    header('Location: user/dashboard.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    SessionService::destroy();
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Login - TLS Wallet</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/auth-mobile.css">
    <link rel="manifest" href="manifest.webmanifest">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="TLS Wallet">
</head>
<body>
    <!-- Mobile-First Navigation -->
    <nav class="mobile-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">🔐</div>
                <div class="brand-text">
                    <h2>TLS Wallet</h2>
                    <span class="brand-subtitle">Secure Login</span>
                </div>
            </div>
            <div class="nav-actions">
                <a href="index.php" class="nav-link">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    <span>Home</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Mobile-First Auth Container -->
    <div class="mobile-auth-container">
        <div class="auth-wrapper">
            <!-- Progress Indicator -->
            <div class="auth-progress">
                <div class="progress-step active" data-step="login">
                    <div class="step-indicator">1</div>
                    <span>Login</span>
                </div>
                <div class="progress-step" data-step="register">
                    <div class="step-indicator">2</div>
                    <span>Register</span>
                </div>
                <div class="progress-step" data-step="forgot">
                    <div class="step-indicator">3</div>
                    <span>Reset</span>
                </div>
            </div>

            <!-- Mobile Auth Header -->
            <div class="mobile-auth-header">
                <div class="auth-logo">
                    <div class="logo-animation">
                        <div class="logo-icon">💎</div>
                        <div class="logo-glow"></div>
                    </div>
                    <h1>Welcome to TLS Wallet</h1>
                </div>
                <p class="auth-subtitle">Your secure gateway to TRON & USDT investments</p>
            </div>

            <!-- Mobile Tab Navigation -->
            <div class="mobile-auth-tabs">
                <button class="mobile-tab-btn active" onclick="window.mobileAuth.showMobileLogin()" data-tab="login">
                    <div class="tab-icon">🔑</div>
                    <span>Sign In</span>
                    <div class="tab-indicator"></div>
                </button>
                <button class="mobile-tab-btn" onclick="window.mobileAuth.showMobileRegister()" data-tab="register">
                    <div class="tab-icon">✨</div>
                    <span>Join Now</span>
                    <div class="tab-indicator"></div>
                </button>
            </div>

            <!-- Mobile Login Form -->
            <form id="mobileLoginForm" class="mobile-auth-form active" novalidate>
                <div class="mobile-form-header">
                    <h2>Welcome Back! 👋</h2>
                    <p>Sign in to continue your investment journey</p>
                </div>

                <!-- Email Input -->
                <div class="mobile-form-group">
                    <label for="mobileLoginEmail" class="mobile-label">
                        <span class="label-text">Email Address</span>
                        <span class="label-required">*</span>
                    </label>
                    <div class="mobile-input-wrapper">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                        </div>
                        <input type="email" id="mobileLoginEmail" name="email" required autocomplete="email"
                               placeholder="<EMAIL>" aria-describedby="mobileLoginEmailError"
                               class="mobile-input">
                        <div class="input-status" id="mobileLoginEmailStatus"></div>
                    </div>
                    <div class="mobile-field-error" id="mobileLoginEmailError"></div>
                </div>

                <!-- Password Input -->
                <div class="mobile-form-group">
                    <label for="mobileLoginPassword" class="mobile-label">
                        <span class="label-text">Password</span>
                        <span class="label-required">*</span>
                    </label>
                    <div class="mobile-input-wrapper">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                <circle cx="12" cy="16" r="1"/>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                            </svg>
                        </div>
                        <input type="password" id="mobileLoginPassword" name="password" required autocomplete="current-password"
                               placeholder="Enter your password" aria-describedby="mobileLoginPasswordError"
                               class="mobile-input">
                        <button type="button" class="mobile-password-toggle" onclick="window.mobileAuth.toggleMobilePassword('mobileLoginPassword')"
                                aria-label="Show password">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                <circle cx="12" cy="12" r="3"/>
                            </svg>
                        </button>
                    </div>
                    <div class="mobile-field-error" id="mobileLoginPasswordError"></div>
                </div>

                <!-- Mobile Form Options -->
                <div class="mobile-form-options">
                    <label class="mobile-checkbox-wrapper">
                        <input type="checkbox" id="mobileRememberMe" name="rememberMe">
                        <div class="mobile-checkmark">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                <polyline points="20,6 9,17 4,12"/>
                            </svg>
                        </div>
                        <span class="checkbox-text">Keep me signed in</span>
                    </label>
                    <button type="button" onclick="window.mobileAuth.showMobileForgotPassword()" class="mobile-forgot-link">
                        Forgot Password?
                    </button>
                </div>

                <!-- Mobile Submit Button -->
                <button type="submit" class="mobile-btn mobile-btn-primary">
                    <span class="btn-content">
                        <span class="btn-text">Sign In to TLS Wallet</span>
                        <div class="btn-loader">
                            <div class="loader-spinner"></div>
                        </div>
                    </span>
                    <div class="btn-ripple"></div>
                </button>

                <!-- Mobile Form Footer -->
                <div class="mobile-form-footer">
                    <p>New to TLS Wallet?</p>
                    <button type="button" onclick="window.mobileAuth.showMobileRegister()" class="mobile-link-btn">
                        Create your account →
                    </button>
                </div>
            </form>

            <!-- Mobile Register Form -->
            <form id="mobileRegisterForm" class="mobile-auth-form" novalidate>
                <div class="mobile-form-header">
                    <h2>Join TLS Wallet ✨</h2>
                    <p>Start your crypto investment journey today</p>
                </div>

                <!-- Registration Steps -->
                <div class="mobile-registration-steps">
                    <div class="step-indicator-bar">
                        <div class="step-progress" id="registrationProgress"></div>
                    </div>
                    <div class="step-labels">
                        <span class="step-label active">Account</span>
                        <span class="step-label">Security</span>
                        <span class="step-label">Complete</span>
                    </div>
                </div>

                <!-- Step 1: Basic Information -->
                <div class="registration-step active" id="step1">
                    <div class="mobile-form-group">
                        <label for="mobileRegisterEmail" class="mobile-label">
                            <span class="label-text">Email Address</span>
                            <span class="label-required">*</span>
                        </label>
                        <div class="mobile-input-wrapper">
                            <div class="input-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                            </div>
                            <input type="email" id="mobileRegisterEmail" name="email" required autocomplete="email"
                                   placeholder="<EMAIL>" aria-describedby="mobileRegisterEmailError"
                                   class="mobile-input">
                            <div class="input-status" id="mobileRegisterEmailStatus"></div>
                        </div>
                        <div class="mobile-field-error" id="mobileRegisterEmailError"></div>
                        <div class="mobile-field-hint">We'll send you a verification email</div>
                    </div>

                    <div class="mobile-form-group">
                        <label for="mobileRegisterPassword" class="mobile-label">
                            <span class="label-text">Create Password</span>
                            <span class="label-required">*</span>
                        </label>
                        <div class="mobile-input-wrapper">
                            <div class="input-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                    <circle cx="12" cy="16" r="1"/>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                </svg>
                            </div>
                            <input type="password" id="mobileRegisterPassword" name="password" required autocomplete="new-password"
                                   placeholder="Create a strong password" aria-describedby="mobileRegisterPasswordError"
                                   class="mobile-input">
                            <button type="button" class="mobile-password-toggle" onclick="toggleMobilePassword('mobileRegisterPassword')"
                                    aria-label="Show password">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                        <div class="mobile-field-error" id="mobileRegisterPasswordError"></div>
                        <div class="mobile-password-strength" id="mobilePasswordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text">Password strength</span>
                        </div>
                    </div>

                    <div class="mobile-form-group">
                        <label for="mobileConfirmPassword" class="mobile-label">
                            <span class="label-text">Confirm Password</span>
                            <span class="label-required">*</span>
                        </label>
                        <div class="mobile-input-wrapper">
                            <div class="input-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                    <path d="M12 16v-4"/>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                </svg>
                            </div>
                            <input type="password" id="mobileConfirmPassword" name="confirmPassword" required autocomplete="new-password"
                                   placeholder="Confirm your password" aria-describedby="mobileConfirmPasswordError"
                                   class="mobile-input">
                            <button type="button" class="mobile-password-toggle" onclick="toggleMobilePassword('mobileConfirmPassword')"
                                    aria-label="Show password">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                        <div class="mobile-field-error" id="mobileConfirmPasswordError"></div>
                    </div>

                    <button type="button" class="mobile-btn mobile-btn-secondary" onclick="window.mobileAuth.nextRegistrationStep()">
                        <span class="btn-content">
                            <span class="btn-text">Continue</span>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,18 15,12 9,6"/>
                            </svg>
                        </span>
                    </button>
                </div>
                
                <!-- Step 2: Security Setup -->
                <div class="registration-step" id="step2">
                    <div class="mobile-form-group">
                        <label for="mobileRegisterPin" class="mobile-label">
                            <span class="label-text">Security PIN</span>
                            <span class="label-required">*</span>
                        </label>
                        <div class="mobile-input-wrapper">
                            <div class="input-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                    <circle cx="8.5" cy="8.5" r="1.5"/>
                                    <circle cx="15.5" cy="8.5" r="1.5"/>
                                    <circle cx="8.5" cy="15.5" r="1.5"/>
                                    <circle cx="15.5" cy="15.5" r="1.5"/>
                                </svg>
                            </div>
                            <input type="password" id="mobileRegisterPin" name="pin" pattern="[0-9]{5}" maxlength="5" minlength="5"
                                   required autocomplete="off" placeholder="5-digit PIN" aria-describedby="mobileRegisterPinError"
                                   class="mobile-input mobile-pin-input">
                        </div>
                        <div class="mobile-field-error" id="mobileRegisterPinError"></div>
                        <div class="mobile-field-hint">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M12 6v6l4 2"/>
                            </svg>
                            Used for transactions and sensitive operations
                        </div>
                    </div>

                    <div class="mobile-form-group">
                        <label for="mobileRegisterReferral" class="mobile-label">
                            <span class="label-text">Referral Code</span>
                            <span class="label-optional">(Optional)</span>
                        </label>
                        <div class="mobile-input-wrapper">
                            <div class="input-icon">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 12v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-6"/>
                                    <path d="M12 2l8 5-8 5-8-5 8-5z"/>
                                </svg>
                            </div>
                            <input type="text" id="mobileRegisterReferral" name="referral" placeholder="Enter referral code"
                                   aria-describedby="mobileRegisterReferralError" class="mobile-input">
                            <div class="input-status" id="mobileRegisterReferralStatus"></div>
                        </div>
                        <div class="mobile-field-error" id="mobileRegisterReferralError"></div>
                        <div class="mobile-field-hint">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            Get bonus rewards with a valid referral code
                        </div>
                    </div>

                    <div class="mobile-step-actions">
                        <button type="button" class="mobile-btn mobile-btn-outline" onclick="window.mobileAuth.previousRegistrationStep()">
                            <span class="btn-content">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="15,18 9,12 15,6"/>
                                </svg>
                                <span class="btn-text">Back</span>
                            </span>
                        </button>
                        <button type="button" class="mobile-btn mobile-btn-secondary" onclick="window.mobileAuth.nextRegistrationStep()">
                            <span class="btn-content">
                                <span class="btn-text">Continue</span>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="9,18 15,12 9,6"/>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>

                <!-- Step 3: Terms and Completion -->
                <div class="registration-step" id="step3">
                    <div class="mobile-terms-section">
                        <div class="terms-header">
                            <h3>Terms & Conditions</h3>
                            <p>Please review and accept our terms to complete registration</p>
                        </div>

                        <div class="mobile-terms-content">
                            <div class="terms-summary">
                                <div class="terms-item">
                                    <div class="terms-icon">🔒</div>
                                    <div class="terms-text">
                                        <h4>Privacy Protection</h4>
                                        <p>Your data is encrypted and secure</p>
                                    </div>
                                </div>
                                <div class="terms-item">
                                    <div class="terms-icon">💰</div>
                                    <div class="terms-text">
                                        <h4>Investment Terms</h4>
                                        <p>Understand risks and rewards</p>
                                    </div>
                                </div>
                                <div class="terms-item">
                                    <div class="terms-icon">📱</div>
                                    <div class="terms-text">
                                        <h4>Service Agreement</h4>
                                        <p>Platform usage guidelines</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-form-group">
                            <label class="mobile-terms-checkbox">
                                <input type="checkbox" id="mobileAgreeTerms" name="agreeTerms" required>
                                <div class="mobile-checkmark">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3">
                                        <polyline points="20,6 9,17 4,12"/>
                                    </svg>
                                </div>
                                <div class="terms-text">
                                    <span>I agree to the <a href="terms.php" target="_blank" class="terms-link">Terms of Service</a> and <a href="privacy.php" target="_blank" class="terms-link">Privacy Policy</a></span>
                                </div>
                            </label>
                            <div class="mobile-field-error" id="mobileAgreeTermsError"></div>
                        </div>
                    </div>

                    <div class="mobile-step-actions">
                        <button type="button" class="mobile-btn mobile-btn-outline" onclick="window.mobileAuth.previousRegistrationStep()">
                            <span class="btn-content">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="15,18 9,12 15,6"/>
                                </svg>
                                <span class="btn-text">Back</span>
                            </span>
                        </button>
                        <button type="submit" class="mobile-btn mobile-btn-primary">
                            <span class="btn-content">
                                <span class="btn-text">Create My Account</span>
                                <div class="btn-loader">
                                    <div class="loader-spinner"></div>
                                </div>
                            </span>
                            <div class="btn-ripple"></div>
                        </button>
                    </div>
                </div>

                <!-- Mobile Form Footer -->
                <div class="mobile-form-footer">
                    <p>Already have an account?</p>
                    <button type="button" onclick="window.mobileAuth.showMobileLogin()" class="mobile-link-btn">
                        Sign in instead →
                    </button>
                </div>
            </form>

            <!-- Mobile Forgot Password Form -->
            <form id="mobileForgotPasswordForm" class="mobile-auth-form" novalidate>
                <div class="mobile-form-header">
                    <h2>Reset Password 🔑</h2>
                    <p>Don't worry, we'll help you get back into your account</p>
                </div>

                <!-- Reset Steps Indicator -->
                <div class="mobile-reset-steps">
                    <div class="reset-step active">
                        <div class="step-icon">📧</div>
                        <span>Enter Email</span>
                    </div>
                    <div class="reset-step">
                        <div class="step-icon">📱</div>
                        <span>Check Email</span>
                    </div>
                    <div class="reset-step">
                        <div class="step-icon">🔒</div>
                        <span>New Password</span>
                    </div>
                </div>

                <!-- Email Input -->
                <div class="mobile-form-group">
                    <label for="mobileForgotEmail" class="mobile-label">
                        <span class="label-text">Email Address</span>
                        <span class="label-required">*</span>
                    </label>
                    <div class="mobile-input-wrapper">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                        </div>
                        <input type="email" id="mobileForgotEmail" name="email" required autocomplete="email"
                               placeholder="<EMAIL>" aria-describedby="mobileForgotEmailError"
                               class="mobile-input">
                        <div class="input-status" id="mobileForgotEmailStatus"></div>
                    </div>
                    <div class="mobile-field-error" id="mobileForgotEmailError"></div>
                    <div class="mobile-field-hint">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 16v-4"/>
                            <path d="M12 8h.01"/>
                        </svg>
                        We'll send a secure reset link to this email address
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="mobile-security-notice">
                    <div class="security-icon">🛡️</div>
                    <div class="security-content">
                        <h4>Security Notice</h4>
                        <p>For your protection, the reset link will expire in 15 minutes and can only be used once.</p>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="mobile-btn mobile-btn-primary">
                    <span class="btn-content">
                        <span class="btn-text">Send Reset Link</span>
                        <div class="btn-loader">
                            <div class="loader-spinner"></div>
                        </div>
                    </span>
                    <div class="btn-ripple"></div>
                </button>

                <!-- Alternative Actions -->
                <div class="mobile-alternative-actions">
                    <button type="button" onclick="window.mobileAuth.showMobileLogin()" class="mobile-link-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="15,18 9,12 15,6"/>
                        </svg>
                        Back to Sign In
                    </button>
                    <button type="button" onclick="window.mobileAuth.showMobileRegister()" class="mobile-link-btn">
                        Create New Account
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="9,18 15,12 9,6"/>
                        </svg>
                    </button>
                </div>
            </form>

            <!-- Mobile Message Area -->
            <div id="mobileMessage" class="mobile-message" role="alert" aria-live="polite"></div>

            <!-- Mobile Security Notice -->
            <div class="mobile-security-notice-footer">
                <div class="security-badges">
                    <div class="security-badge">
                        <div class="badge-icon">🔒</div>
                        <div class="badge-text">
                            <span class="badge-title">SSL Encrypted</span>
                            <span class="badge-subtitle">256-bit security</span>
                        </div>
                    </div>
                    <div class="security-badge">
                        <div class="badge-icon">🛡️</div>
                        <div class="badge-text">
                            <span class="badge-title">Privacy Protected</span>
                            <span class="badge-subtitle">GDPR compliant</span>
                        </div>
                    </div>
                    <div class="security-badge">
                        <div class="badge-icon">⚡</div>
                        <div class="badge-text">
                            <span class="badge-title">Fast & Secure</span>
                            <span class="badge-subtitle">Instant access</span>
                        </div>
                    </div>
                </div>
                <p class="security-footer-text">
                    Your data is protected with bank-level encryption. We never store passwords in plain text.
                </p>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation Helper -->
    <div class="mobile-bottom-helper">
        <div class="helper-content">
            <div class="helper-icon">💡</div>
            <span>Swipe up for more options</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="user/js/csrf-manager.js"></script>
    <script src="user/js/auth.js"></script>
    <script src="user/js/auth-mobile.js"></script>
</body>
</html>
