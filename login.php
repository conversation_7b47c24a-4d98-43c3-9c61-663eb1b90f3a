<?php
// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
$isLoggedIn = SessionService::isAuthenticated();
$isAdmin = SessionService::isAdmin();

// Redirect to admin if admin user
if ($isLoggedIn && $isAdmin && !isset($_GET['user_view'])) {
    header('Location: admin.php');
    exit;
}

// Redirect to dashboard if logged in
if ($isLoggedIn && !isset($_GET['logout'])) {
    header('Location: user/dashboard.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    SessionService::destroy();
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - TLS Wallet</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body>
    <!-- Public Navigation -->
    <nav class="public-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>TLS Wallet</h2>
            </div>
            <div class="nav-links">
                <a href="index.php" class="nav-link">Home</a>
                <a href="invest-info.php" class="nav-link">Invest</a>
                <a href="faq.php" class="nav-link">FAQ</a>
            </div>
        </div>
    </nav>

    <div class="auth-container">
        <div class="auth-wrapper">
            <div class="auth-header">
                <div class="auth-logo">
                    <div class="logo-icon">🔐</div>
                    <h1>TLS Wallet</h1>
                </div>
                <p class="auth-subtitle">TRON · USDT · Secure cryptocurrency wallet management</p>
            </div>

            <div class="auth-tabs">
                <button class="tab-btn active" onclick="showLogin()" aria-label="Switch to login form">
                    <span class="tab-icon">🔑</span>
                    Login
                </button>
                <button class="tab-btn" onclick="showRegister()" aria-label="Switch to registration form">
                    <span class="tab-icon">👤</span>
                    Register
                </button>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form active" novalidate>
                <div class="form-header">
                    <h2>Welcome Back</h2>
                    <p>Sign in to your TLS Wallet account</p>
                </div>
                
                <div class="form-group">
                    <label for="loginEmail">Email Address</label>
                    <div class="input-wrapper">
                        <span class="input-icon">📧</span>
                        <input type="email" id="loginEmail" name="email" required autocomplete="email" 
                               placeholder="Enter your email" aria-describedby="loginEmailError">
                    </div>
                    <div class="field-error" id="loginEmailError"></div>
                </div>
                
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔒</span>
                        <input type="password" id="loginPassword" name="password" required autocomplete="current-password" 
                               placeholder="Enter your password" aria-describedby="loginPasswordError">
                        <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')" 
                                aria-label="Show password">👁️</button>
                    </div>
                    <div class="field-error" id="loginPasswordError"></div>
                </div>
                
                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="#" onclick="showForgotPassword()" class="forgot-link">Forgot Password?</a>
                </div>
                
                <button type="submit" class="btn btn-primary btn-auth">
                    <span class="btn-text">Sign In</span>
                    <span class="btn-loader"></span>
                </button>
                
                <div class="form-footer">
                    <p>Don't have an account? <a href="#" onclick="showRegister()">Create one</a></p>
                </div>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="auth-form" novalidate>
                <div class="form-header">
                    <h2>Create Account</h2>
                    <p>Join TLS Wallet and start investing</p>
                </div>
                
                <div class="form-group">
                    <label for="registerEmail">Email Address</label>
                    <div class="input-wrapper">
                        <span class="input-icon">📧</span>
                        <input type="email" id="registerEmail" name="email" required autocomplete="email" 
                               placeholder="Enter your email" aria-describedby="registerEmailError">
                    </div>
                    <div class="field-error" id="registerEmailError"></div>
                    <div class="field-hint">We'll send you a verification email</div>
                </div>
                
                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔒</span>
                        <input type="password" id="registerPassword" name="password" required autocomplete="new-password" 
                               placeholder="Create a strong password" aria-describedby="registerPasswordError">
                        <button type="button" class="password-toggle" onclick="togglePassword('registerPassword')" 
                                aria-label="Show password">👁️</button>
                    </div>
                    <div class="field-error" id="registerPasswordError"></div>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔒</span>
                        <input type="password" id="confirmPassword" name="confirmPassword" required autocomplete="new-password" 
                               placeholder="Confirm your password" aria-describedby="confirmPasswordError">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')" 
                                aria-label="Show password">👁️</button>
                    </div>
                    <div class="field-error" id="confirmPasswordError"></div>
                </div>
                
                <div class="form-group">
                    <label for="registerPin">Security PIN</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔢</span>
                        <input type="password" id="registerPin" name="pin" pattern="[0-9]{5}" maxlength="5" minlength="5" 
                               required autocomplete="off" placeholder="5-digit PIN" aria-describedby="registerPinError">
                    </div>
                    <div class="field-error" id="registerPinError"></div>
                    <div class="field-hint">Used for transactions and sensitive operations</div>
                </div>
                
                <div class="form-group">
                    <label for="registerReferral">Referral Code <span class="optional">(Optional)</span></label>
                    <div class="input-wrapper">
                        <span class="input-icon">🎁</span>
                        <input type="text" id="registerReferral" name="referral" placeholder="Enter referral code" 
                               aria-describedby="registerReferralError">
                    </div>
                    <div class="field-error" id="registerReferralError"></div>
                    <div class="field-hint">Get bonus rewards with a valid referral code</div>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-wrapper terms-checkbox">
                        <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                        <span class="checkmark"></span>
                        I agree to the <a href="terms.php" target="_blank">Terms of Service</a> and <a href="privacy.php" target="_blank">Privacy Policy</a>
                    </label>
                    <div class="field-error" id="agreeTermsError"></div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-auth">
                    <span class="btn-text">Create Account</span>
                    <span class="btn-loader"></span>
                </button>
                
                <div class="form-footer">
                    <p>Already have an account? <a href="#" onclick="showLogin()">Sign in</a></p>
                </div>
            </form>

            <!-- Forgot Password Form -->
            <form id="forgotPasswordForm" class="auth-form" novalidate>
                <div class="form-header">
                    <h2>Reset Password</h2>
                    <p>Enter your email to receive a password reset link</p>
                </div>
                
                <div class="form-group">
                    <label for="forgotEmail">Email Address</label>
                    <div class="input-wrapper">
                        <span class="input-icon">📧</span>
                        <input type="email" id="forgotEmail" name="email" required autocomplete="email" 
                               placeholder="Enter your email" aria-describedby="forgotEmailError">
                    </div>
                    <div class="field-error" id="forgotEmailError"></div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-auth">
                    <span class="btn-text">Send Reset Link</span>
                    <span class="btn-loader"></span>
                </button>
                
                <div class="form-footer">
                    <p><a href="#" onclick="showLogin()">← Back to Login</a></p>
                </div>
            </form>

            <div id="message" class="message" role="alert" aria-live="polite"></div>
            
            <!-- Security Notice -->
            <div class="security-notice">
                <div class="security-icon">🛡️</div>
                <div class="security-text">
                    <strong>Your security is our priority</strong>
                    <p>We use bank-level encryption and never store your passwords in plain text.</p>
                </div>
            </div>
        </div>
    </div>    <script src="user/js/csrf-manager.js"></script>
    <script src="user/js/auth.js"></script>
</body>
</html>
