// CSRF Protection JavaScript Module
// Handles CSRF token management for secure AJAX requests

class CSRFManager {
    constructor() {
        this.token = null;
        this.expiresAt = null;
        this.context = 'user';
        this.retryCount = 0;
        this.maxRetries = 3;
    }

    /**
     * Set the context for CSRF tokens (user or admin)
     * @param {string} context - The context ('user' or 'admin')
     */
    setContext(context) {
        this.context = context;
    }

    /**
     * Get a fresh CSRF token
     * @returns {Promise<string>} The CSRF token
     */
    async getToken() {
        try {
            // Check if current token is still valid
            if (this.token && this.expiresAt && Date.now() < this.expiresAt) {
                return this.token;
            }

            // Determine the correct path to ajax.php based on current location
            let ajaxPath = '../ajax.php';
            if (window.location.pathname.includes('/user/')) {
                ajaxPath = '../ajax.php';
            } else if (window.location.pathname.includes('/admin')) {
                ajaxPath = 'ajax.php';
            } else {
                // We're in the root directory
                ajaxPath = 'ajax.php';
            }

            // Request new token
            const response = await fetch(ajaxPath, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'get_csrf_token',
                    context: this.context
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Failed to get CSRF token');
            }

            // Store token and expiration
            this.token = data.csrf_token;
            this.expiresAt = Date.now() + (data.expires_in * 1000) - 30000; // 30 seconds buffer
            this.retryCount = 0;

            return this.token;

        } catch (error) {
            console.error('Failed to get CSRF token:', error);
            
            // Return null on failure
            this.token = null;
            this.expiresAt = null;
            return null;
        }
    }

    /**
     * Make a secure AJAX request with CSRF protection
     * @param {string} action - The action to perform
     * @param {Object} data - The request data
     * @param {Object} options - Additional options
     * @returns {Promise<Object>} The response data
     */
    async secureRequest(action, data = {}, options = {}) {
        try {
            // Get CSRF token
            const token = await this.getToken();
            
            if (!token) {
                throw new Error('Unable to obtain CSRF token');
            }

            // Prepare request data
            const requestData = {
                action: action,
                csrf_token: token,
                ...data
            };

            // Determine the correct path to ajax.php
            let defaultUrl = '../ajax.php';
            if (window.location.pathname.includes('/user/')) {
                defaultUrl = '../ajax.php';
            } else if (window.location.pathname.includes('/admin')) {
                defaultUrl = 'ajax.php';
            } else {
                defaultUrl = 'ajax.php';
            }

            // Make request
            const query  ={
                method: options.method || 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: JSON.stringify(requestData)
            };
            const response = await fetch(options.url || defaultUrl, query);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseData = await response.json();
            // Handle CSRF-related errors
            if (responseData.error && responseData.code === 403) {
                if (responseData.action_required === 'refresh_token' && this.retryCount < this.maxRetries) {
                    // Token expired, get new one and retry
                    this.token = null;
                    this.expiresAt = null;
                    this.retryCount++;
                    
                    console.log(`CSRF token expired, retrying... (${this.retryCount}/${this.maxRetries})`);
                    return await this.secureRequest(action, data, options);
                } else if (responseData.action_required === 'refresh_page') {
                    // Session issue, recommend page refresh
                    this.handleSecurityError('Session security issue detected. Please refresh the page.');
                    return null;
                }
            }

            this.retryCount = 0;
            return responseData;

        } catch (error) {
            console.error('Secure request failed:', error);
            throw error;
        }
    }

    /**
     * Make a request for state-changing operations
     * @param {string} action - The action to perform
     * @param {Object} data - The request data
     * @returns {Promise<Object>} The response data
     */
    async stateChangingRequest(action, data = {}) {
        const stateChangingActions = [
            'register', 'login', 'change_password', 'withdraw', 'record_deposit',            'create_investment', 'admin_promote_user', 'admin_demote_user',
            'admin_activate_user', 'admin_deactivate_user', 'admin_clear_cache',
            'admin_backup_db', 'confirm_payment'
        ];

        if (stateChangingActions.includes(action)) {
            return await this.secureRequest(action, data);
        } else {
            // For read-only operations, CSRF token is optional
            return await this.regularRequest(action, data);
        }
    }

    /**
     * Make a regular AJAX request without CSRF protection
     * @param {string} action - The action to perform
     * @param {Object} data - The request data
     * @returns {Promise<Object>} The response data
     */
    async regularRequest(action, data = {}) {
        try {
            // Determine the correct path to ajax.php
            let ajaxPath = '../ajax.php';
            if (window.location.pathname.includes('/user/')) {
                ajaxPath = '../ajax.php';
            } else if (window.location.pathname.includes('/admin')) {
                ajaxPath = 'ajax.php';
            } else {
                ajaxPath = 'ajax.php';
            }
            
            const response = await fetch(ajaxPath, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: action,
                    ...data
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Regular request failed:', error);
            throw error;
        }
    }

    /**
     * Handle security-related errors
     * @param {string} message - The error message
     */
    handleSecurityError(message) {
        // Show user-friendly error message
        if (typeof showMessage === 'function') {
            showMessage(message, 'error');
        } else {
            alert(message);
        }

        // Log security event
        console.warn('CSRF Security Event:', message);

        // Optionally redirect to login or refresh page
        if (message.includes('refresh the page')) {
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
    }

    /**
     * Clear stored tokens (e.g., on logout)
     */
    clearTokens() {
        this.token = null;
        this.expiresAt = null;
        this.retryCount = 0;
    }

    /**
     * Get token statistics (for debugging)
     * @returns {Object} Token statistics
     */
    getStats() {
        return {
            hasToken: !!this.token,
            context: this.context,
            expiresAt: this.expiresAt,
            isExpired: this.expiresAt ? Date.now() >= this.expiresAt : null,
            retryCount: this.retryCount
        };
    }
}

// Create global CSRF manager instance
window.csrfManager = new CSRFManager();

// Utility functions for backward compatibility
window.secureAjax = async function(action, data = {}) {
    return await window.csrfManager.stateChangingRequest(action, data);
};

window.regularAjax = async function(action, data = {}) {
    return await window.csrfManager.regularRequest(action, data);
};

// Auto-initialize based on page context
document.addEventListener('DOMContentLoaded', function() {
    // Detect if we're on admin page
    if (window.location.pathname.includes('admin') || document.body.classList.contains('admin-page')) {
        window.csrfManager.setContext('admin');
    }
    
    // Pre-fetch token for better user experience
    window.csrfManager.getToken().catch(error => {
        console.log('Pre-fetch token failed (this is normal for public pages):', error.message);
    });
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CSRFManager;
}
