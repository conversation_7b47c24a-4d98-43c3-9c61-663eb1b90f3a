/* Investment Info page specific styles */

/* CSS Variables for Investment Info page */
:root {
    /* Light mode Investment Info colors */
    --invest-hero-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --invest-hero-text: white;
    --invest-plans-bg: #f8f9fa;
    --invest-plan-bg: white;
    --invest-plan-shadow: rgba(0, 0, 0, 0.1);
    --invest-plan-border: #e2e8f0;
    --invest-plan-popular-bg: #667eea;
    --invest-plan-popular-text: white;
    --invest-plan-title: #2d3748;
    --invest-plan-price: #667eea;
    --invest-plan-feature: #4a5568;
    --invest-calculator-bg: #2d3748;
    --invest-calculator-text: white;
    --invest-calculator-input-bg: rgba(255, 255, 255, 0.1);
    --invest-calculator-input-border: rgba(255, 255, 255, 0.2);
    --invest-calculator-input-text: white;
    --invest-how-bg: white;
    --invest-how-step-bg: #f7fafc;
    --invest-how-step-icon: #667eea;
}

/* Dark mode Investment Info colors */
@media (prefers-color-scheme: dark) {
    :root {
        --invest-hero-gradient: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        --invest-hero-text: #f7fafc;
        --invest-plans-bg: var(--bg-primary);
        --invest-plan-bg: var(--bg-secondary);
        --invest-plan-shadow: rgba(0, 0, 0, 0.3);
        --invest-plan-border: var(--border-color);
        --invest-plan-popular-bg: var(--primary-color);
        --invest-plan-popular-text: white;
        --invest-plan-title: var(--text-primary);
        --invest-plan-price: var(--primary-color);
        --invest-plan-feature: var(--text-secondary);
        --invest-calculator-bg: var(--bg-secondary);
        --invest-calculator-text: var(--text-primary);
        --invest-calculator-input-bg: var(--bg-tertiary);
        --invest-calculator-input-border: var(--border-color);
        --invest-calculator-input-text: var(--text-primary);
        --invest-how-bg: var(--bg-secondary);
        --invest-how-step-bg: var(--bg-tertiary);
        --invest-how-step-icon: var(--primary-color);
    }
}

/* Hero Section */
.invest-hero {
    background: var(--invest-hero-gradient);
    color: var(--invest-hero-text);
    padding: 120px 0 80px;
    text-align: center;
}

.invest-hero h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.hero-stats .stat {
    text-align: center;
}

.hero-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 8px;
    color: #fff;
}

.hero-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
}

/* Calculator Section */
.calculator-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.calculator-card {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.calculator-card h2 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 12px;
    color: #2c3e50;
}

.calculator-card p {
    color: #666;
    margin-bottom: 40px;
    font-size: 1.1rem;
}

.calculator {
    display: grid;
    gap: 24px;
}

.calc-input-group {
    text-align: left;
}

.calc-input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.calc-input-group input,
.calc-input-group select {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background: white;
}

.calc-input-group input:focus,
.calc-input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.calc-results {
    background: var(--invest-hero-gradient);
    border-radius: 16px;
    padding: 32px;
    color: white;
    margin-top: 24px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item.total {
    font-size: 1.2rem;
    font-weight: 700;
}

.result-label {
    font-weight: 500;
}

.result-value {
    font-weight: 700;
    font-size: 1.1rem;
}

.result-value.profit {
    color: #ffd700;
}

/* Plans Section */
.plans-section {
    padding: 80px 0;
    background: white;
}

.plans-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 32px;
    max-width: 1200px;
    margin: 0 auto;
}

.plan-card {
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
}

.plan-card.popular {
    border-color: #007bff;
    transform: scale(1.02);
    position: relative;
}

.plan-card.popular:hover {
    transform: scale(1.02) translateY(-8px);
}

.plan-badge {
    position: absolute;
    top: 20px;
    right: -35px;
    background: #28a745;
    color: white;
    padding: 8px 45px;
    font-size: 0.8rem;
    font-weight: 600;
    transform: rotate(45deg);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.popular-badge {
    background: #007bff;
}

.plan-header {
    text-align: center;
    margin-bottom: 32px;
}

.plan-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.plan-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 12px;
    color: #2c3e50;
}

.plan-return {
    font-size: 3rem;
    font-weight: 800;
    color: #28a745;
    line-height: 1;
}

.plan-return span {
    font-size: 1rem;
    font-weight: 500;
    color: #666;
    display: block;
    margin-top: 4px;
}

.plan-details {
    margin-bottom: 32px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row .label {
    color: #666;
    font-weight: 500;
}

.detail-row .value {
    font-weight: 600;
    color: #2c3e50;
}

.plan-features {
    margin-bottom: 32px;
}

.plan-features .feature {
    padding: 8px 0;
    color: #28a745;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.plan-example {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
}

.plan-example h4 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
}

.example-breakdown {
    display: grid;
    gap: 8px;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.breakdown-item:last-child {
    border-bottom: none;
}

.breakdown-item.total {
    font-weight: 700;
    color: #28a745;
    font-size: 1.1rem;
    padding-top: 16px;
    border-top: 2px solid #28a745;
}

.plan-btn {
    display: block;
    width: 100%;
    padding: 16px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.plan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.plan-btn.featured {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Disabled plan styles */
.plan-btn.disabled {
    background: #6c757d;
    color: #adb5bd;
    cursor: not-allowed;
    opacity: 0.6;
    border: none;
    font-size: 1rem;
}

.plan-btn.disabled:hover {
    transform: none;
    box-shadow: none;
}

.plan-card:has(.plan-btn.disabled) {
    opacity: 0.7;
    position: relative;
}

.plan-card:has(.plan-btn.disabled)::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 16px;
    pointer-events: none;
}

.plan-status-note {
    margin-top: 12px;
    padding: 8px 12px;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 6px;
    text-align: center;
}

.plan-status-note small {
    color: #dc3545;
    font-size: 0.85rem;
    line-height: 1.3;
}

/* How It Works */
.how-it-works {
    padding: 80px 0;
    background: #f8f9fa;
}

.steps-timeline {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    margin-bottom: 48px;
    position: relative;
}

.step-item:last-child {
    margin-bottom: 0;
}

.step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
    z-index: 2;
    position: relative;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.step-content p {
    color: #666;
    line-height: 1.6;
}

.step-connector {
    width: 3px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: -20px 0 -20px 28.5px;
    z-index: 1;
}

/* Risk Section */
.risk-section {
    padding: 80px 0;
    background: white;
}

.risk-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 48px;
    align-items: center;
}

.risk-text h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.risk-text p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 32px;
}

.security-features {
    display: grid;
    gap: 20px;
    margin-bottom: 32px;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.security-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.security-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.security-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.risk-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 12px;
    padding: 24px;
    border-left: 4px solid #ffc107;
}

.risk-warning h3 {
    color: #856404;
    margin-bottom: 12px;
    font-weight: 600;
}

.risk-warning p {
    color: #856404;
    margin: 0;
    font-size: 0.95rem;
}

.security-chart {
    display: flex;
    align-items: end;
    gap: 20px;
    height: 200px;
    justify-content: center;
}

.chart-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.chart-bar {
    width: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px 6px 0 0;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.chart-bar:hover {
    transform: scale(1.05);
}

.chart-value {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.chart-label {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
    font-weight: 500;
}

/* Investment FAQ */
.investment-faq {
    padding: 80px 0;
    background: #f8f9fa;
}

.investment-faq .faq-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    max-width: 800px;
    margin: 0 auto;
}

.investment-faq .faq-item {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.investment-faq .faq-question {
    padding: 24px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.investment-faq .faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
    padding-right: 16px;
}

.investment-faq .faq-toggle {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    transition: transform 0.3s ease;
}

.investment-faq .faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.investment-faq .faq-answer {
    padding: 0 24px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.investment-faq .faq-item.active .faq-answer {
    padding: 0 24px 24px;
    max-height: 300px;
}

.investment-faq .faq-answer p {
    color: #555;
    line-height: 1.6;
    margin: 0;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.cta-stat {
    text-align: center;
}

.cta-stat .stat-number {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 8px;
}

.cta-stat .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
}

/* Mobile Styles */
@media (max-width: 576px) {
    .invest-hero {
        padding: 100px 0 60px;
    }
    
    .invest-hero h1 {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        gap: 24px;
    }
    
    .hero-stats .stat-number {
        font-size: 2rem;
    }
    
    .calculator-card {
        margin: 0 16px;
        padding: 32px 24px;
    }
    
    .plan-card {
        margin: 0 16px;
        padding: 24px;
    }
    
    .plan-return {
        font-size: 2.5rem;
    }
    
    .step-item {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .step-connector {
        display: none;
    }
    
    .security-chart {
        gap: 12px;
    }
    
    .chart-bar {
        width: 50px;
    }
    
    .cta-stats {
        gap: 24px;
    }
    
    .cta-content h2 {
        font-size: 2rem;
    }
}

/* Tablet Styles */
@media (min-width: 577px) {
    .calculator {
        grid-template-columns: 1fr 1fr;
        align-items: end;
    }
    
    .calc-results {
        grid-column: 1 / -1;
    }
    
    .plans-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .risk-content {
        grid-template-columns: 1fr 1fr;
    }
    
    .investment-faq .faq-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .invest-hero h1 {
        font-size: 3.5rem;
    }
    
    .plans-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .step-item:nth-child(even) {
        flex-direction: row-reverse;
    }
    
    .step-item:nth-child(even) .step-content {
        text-align: right;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .invest-hero h1 {
        font-size: 4rem;
    }
}
