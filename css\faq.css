/* FAQ page specific styles with Dark Mode Support */

/* CSS Variables for FAQ page */
:root {
    /* Light mode FAQ colors */
    --faq-header-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --faq-header-text: white;
    --faq-content-bg: #f8f9fa;
    --faq-search-bg: white;
    --faq-search-shadow: rgba(0, 0, 0, 0.1);
    --faq-search-btn-bg: #667eea;
    --faq-search-btn-hover: #5a67d8;
    --faq-category-bg: #e2e8f0;
    --faq-category-text: #4a5568;
    --faq-category-active-bg: #667eea;
    --faq-category-active-text: white;
    --faq-item-bg: white;
    --faq-item-shadow: rgba(0, 0, 0, 0.1);
    --faq-item-border: #e2e8f0;
    --faq-question-text: #2d3748;
    --faq-answer-text: #4a5568;
    --faq-support-bg: #f7fafc;
    --faq-support-border: #e2e8f0;
}

/* Dark mode FAQ colors */
@media (prefers-color-scheme: dark) {
    :root {
        --faq-header-gradient: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        --faq-header-text: #f7fafc;
        --faq-content-bg: var(--bg-primary);
        --faq-search-bg: var(--bg-secondary);
        --faq-search-shadow: rgba(0, 0, 0, 0.3);
        --faq-search-btn-bg: var(--primary-color);
        --faq-search-btn-hover: var(--primary-hover);
        --faq-category-bg: var(--bg-tertiary);
        --faq-category-text: var(--text-secondary);
        --faq-category-active-bg: var(--primary-color);
        --faq-category-active-text: white;
        --faq-item-bg: var(--bg-secondary);
        --faq-item-shadow: rgba(0, 0, 0, 0.3);
        --faq-item-border: var(--border-color);
        --faq-question-text: var(--text-primary);
        --faq-answer-text: var(--text-secondary);
        --faq-support-bg: var(--bg-secondary);
        --faq-support-border: var(--border-color);
    }
}

/* Existing FAQ styles */

.page-header {
    background: var(--faq-header-gradient);
    color: var(--faq-header-text);
    padding: 100px 0 60px;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.faq-content {
    padding: 80px 0;
    background: var(--faq-content-bg);
}

/* Search Box */
.faq-search {
    margin-bottom: 40px;
    text-align: center;
}

.search-box {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    background: var(--faq-search-bg);
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 4px 20px var(--faq-search-shadow);
}

.search-input {
    flex: 1;
    padding: 16px 24px;
    border: none;
    outline: none;
    font-size: 16px;
    background: transparent;
}

.search-btn {
    padding: 16px 20px;
    background: var(--faq-search-btn-bg);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 18px;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: var(--faq-search-btn-hover);
}

/* Category Filters */
.faq-categories {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 12px 20px;
    background: var(--faq-category-bg);
    border: 2px solid #e9ecef;
    border-radius: 25px;
    color: var(--faq-category-text);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.category-btn:hover,
.category-btn.active {
    background: var(--faq-category-active-bg);
    color: var(--faq-category-active-text);
    border-color: transparent;
    transform: translateY(-2px);
}

/* FAQ Grid */
.faq-grid {
    max-width: 900px;
    margin: 0 auto;
}

.faq-section {
    margin-bottom: 48px;
}

.faq-section.hidden {
    display: none;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 3px solid #667eea;
    display: inline-block;
}

/* FAQ Items */
.faq-item {
    background: var(--faq-item-bg);
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 10px var(--faq-item-shadow);
    border: 1px solid var(--faq-item-border);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.faq-item.hidden {
    display: none;
}

.faq-question {
    padding: 24px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--faq-question-text);
    line-height: 1.4;
    flex: 1;
    padding-right: 16px;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 24px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.faq-item.active .faq-answer {
    padding: 0 24px 24px;
    max-height: 1000px;
}

.faq-answer p {
    color: var(--faq-answer-text);
    line-height: 1.6;
    margin-bottom: 16px;
}

.faq-answer p:last-child {
    margin-bottom: 0;
}

.faq-answer ul,
.faq-answer ol {
    color: var(--faq-answer-text);
    line-height: 1.6;
    margin-bottom: 16px;
    padding-left: 20px;
}

.faq-answer li {
    margin-bottom: 8px;
}

.faq-answer strong {
    color: #2c3e50;
}

/* Support Section */
.support-section {
    margin-top: 60px;
    padding-top: 60px;
    border-top: 1px solid #e9ecef;
}

.support-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.support-card h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 16px;
}

.support-card p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 32px;
}

.support-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.support-option {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px;
    background: #f8f9fa;
    border-radius: 12px;
    text-align: left;
}

.support-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.support-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.support-info p {
    color: #666;
    margin-bottom: 16px;
    font-size: 0.95rem;
}

/* No Results Message */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 16px;
    color: #2c3e50;
}

.no-results p {
    font-size: 1.1rem;
    margin-bottom: 24px;
}

/* Mobile Styles */
@media (max-width: 576px) {
    .page-header {
        padding: 80px 0 40px;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .faq-content {
        padding: 60px 0;
    }
    
    .faq-categories {
        gap: 6px;
    }
    
    .category-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .search-box {
        margin: 0 16px;
    }
    
    .faq-question {
        padding: 20px;
    }
    
    .faq-question h3 {
        font-size: 1rem;
        padding-right: 12px;
    }
    
    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }
    
    .support-card {
        margin: 0 16px;
        padding: 32px 24px;
    }
    
    .support-option {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .support-info {
        text-align: center;
    }
}

/* Tablet Styles */
@media (min-width: 577px) {
    .support-options {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Styles */
@media (min-width: 992px) {
    .page-header h1 {
        font-size: 3rem;
    }
    
    .faq-question h3 {
        font-size: 1.2rem;
    }
    
    .support-card {
        padding: 48px;
    }
}

/* Animation for FAQ items */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.faq-item {
    animation: fadeIn 0.5s ease forwards;
}

/* Search highlighting */
.highlight {
    background: yellow;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Loading state */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
