// Transactions Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    initTransactionsPage();
});

let currentPage = 0;
let currentLimit = 10;

function initTransactionsPage() {
    setupPagination();
    loadTransactions();
    initFooterMenu();
}

function setupPagination() {
    // Pagination
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentPage > 0) {
                currentPage--;
                loadTransactions();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            currentPage++;
            loadTransactions();
        });
    }
}

async function loadTransactions() {
    const transactionsList = document.getElementById('transactionsList');
    
    if (!transactionsList) return;
    
    transactionsList.innerHTML = '<div class="loading">Loading transactions...</div>';
    
    try {
        const filters = {
            type: '', // No filtering
            status: '', // No filtering
            page: currentPage,
            limit: currentLimit
        };
        
        const response = await apiCall('get_transactions', filters);
        
        if (response.success) {
            displayTransactions(response.transactions);
            updatePagination(response.pagination);
        } else {
            transactionsList.innerHTML = '<div class="no-data">Failed to load transactions</div>';
        }
    } catch (error) {
        console.error('Error loading transactions:', error);
        transactionsList.innerHTML = '<div class="no-data">Error loading transactions</div>';
    }
}

function displayTransactions(transactions) {
    const transactionsList = document.getElementById('transactionsList');
    
    if (!transactions || transactions.length === 0) {
        transactionsList.innerHTML = '<div class="no-data">No transactions found</div>';
        return;
    }        const transactionsHTML = transactions.map(tx => `
        <div class="transaction-item">
            <div class="transaction-header">
                <span class="transaction-type ${securityUtils.escapeHtml(tx.type)}">${securityUtils.escapeHtml(formatTransactionType(tx.type))}</span>
                <span class="transaction-status ${securityUtils.escapeHtml(tx.status)}">${securityUtils.escapeHtml(formatStatus(tx.status))}</span>
            </div>
            <div class="transaction-details">
                <div class="transaction-amount ${tx.type === 'withdrawal' ? 'negative' : 'positive'}">
                    ${tx.type === 'withdrawal' ? '-' : '+'}${securityUtils.escapeHtml(tx.amount_formatted || tx.amount || '0.00')} ${securityUtils.escapeHtml(tx.currency || 'TRX')}
                </div>
                <div class="transaction-info">
                    <div class="transaction-hash">
                        <span class="label">Hash:</span>
                        <span class="hash" title="${securityUtils.escapeHtml(tx.hash || tx.transaction_hash || 'Pending')}">${securityUtils.escapeHtml(
                            tx.hash || tx.transaction_hash ? 
                            (tx.hash || tx.transaction_hash).substring(0, 16) + '...' : 
                            'Pending'
                        )}</span>
                    </div>
                    <div class="transaction-date">
                        <span class="label">Date:</span>
                        <span class="date">${securityUtils.escapeHtml(formatDate(tx.created_at))}</span>
                    </div>
                    ${tx.to_address ? `
                    <div class="transaction-address">
                        <span class="label">To:</span>
                        <span class="address" title="${securityUtils.escapeHtml(tx.to_address)}">${securityUtils.escapeHtml(tx.to_address.substring(0, 16))}...</span>
                    </div>
                    ` : ''}
                    ${tx.from_address ? `
                    <div class="transaction-address">
                        <span class="label">From:</span>
                        <span class="address" title="${securityUtils.escapeHtml(tx.from_address)}">${securityUtils.escapeHtml(tx.from_address.substring(0, 16))}...</span>
                    </div>
                    ` : ''}
                    ${tx.note ? `
                    <div class="transaction-note">
                        <span class="label">Note:</span>
                        <span class="note">${securityUtils.escapeHtml(tx.note)}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    transactionsList.innerHTML = transactionsHTML;
}

function updatePagination(pagination) {
    const pageInfo = document.getElementById('pageInfo');
    const paginationInfo = document.getElementById('paginationInfo');
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
      if (pageInfo) {
        pageInfo.textContent = `Page ${securityUtils.escapeHtml(currentPage + 1)}`;
    }
    
    if (paginationInfo && pagination) {
        const start = currentPage * currentLimit + 1;
        const end = Math.min(start + currentLimit - 1, pagination.total || 0);
        const total = pagination.total || 0;
        paginationInfo.textContent = `Showing ${start}-${end} of ${total} transactions`;
    }
    
    if (prevBtn) {
        prevBtn.disabled = currentPage === 0;
    }
    
    if (nextBtn) {
        nextBtn.disabled = !pagination || !pagination.has_more;
    }
}

function formatTransactionType(type) {
    const types = {
        'deposit': 'Deposit',
        'withdrawal': 'Withdrawal',
        'transfer': 'Transfer',
        'sweep': 'Sweep'
    };
    return types[type] || type;
}

function formatStatus(status) {
    const statuses = {
        'pending': 'Pending',
        'confirmed': 'Confirmed',
        'failed': 'Failed'
    };
    return statuses[status] || status;
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (error) {
        return 'Invalid Date';
    }
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: endpoint,
                ...(data || {})
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

function showMessage(message, type = 'info') {
    // Create message element if it doesn't exist
    let messageDiv = document.getElementById('message');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'message';
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '9999';
        messageDiv.style.padding = '12px 24px';
        messageDiv.style.borderRadius = '8px';
        messageDiv.style.fontWeight = '600';
        messageDiv.style.display = 'none';
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    
    // Style based on type
    switch(type) {
        case 'success':
            messageDiv.style.background = '#d4edda';
            messageDiv.style.color = '#155724';
            messageDiv.style.border = '1px solid #c3e6cb';
            break;
        case 'error':
            messageDiv.style.background = '#f8d7da';
            messageDiv.style.color = '#721c24';
            messageDiv.style.border = '1px solid #f5c6cb';
            break;
        case 'warning':
            messageDiv.style.background = '#fff3cd';
            messageDiv.style.color = '#856404';
            messageDiv.style.border = '1px solid #ffeaa7';
            break;
        default:
            messageDiv.style.background = '#d1ecf1';
            messageDiv.style.color = '#0c5460';
            messageDiv.style.border = '1px solid #bee5eb';
    }
    
    messageDiv.style.display = 'block';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

function initFooterMenu() {
    // Add smooth entrance animation for footer menu
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Always show footer menu
        footerMenu.classList.add('show');
        
        // Optional: Handle window resize for future mobile-specific behavior
        window.addEventListener('resize', function() {
            // Footer is always visible now, but this can be customized later
            footerMenu.classList.add('show');
        });
    }
}
