<?php
// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;
use Frontend\Services\ApiService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
$isLoggedIn = SessionService::isAuthenticated();

// Get investment plans from API
$apiService = new ApiService();
$investmentPlansResponse = $apiService->getInvestmentPlans();
$investmentPlans = $investmentPlansResponse['data'] ?? [];

// Fallback plans if API fails
if (empty($investmentPlans)) {
    $investmentPlans = [
        [
            'id' => 1,
            'plan_name' => 'Starter Plan',
            'daily_rate' => 0.08,
            'min_amount' => 100,
            'max_amount' => 999,
            'duration' => 15,
            'description' => 'Perfect for beginners',
            'features' => ['Daily profit payments', 'Principal protection', '24/7 customer support', 'Instant withdrawals', 'Mobile-friendly interface'],
            'is_featured' => false,
            'is_active' => true,
            'badge' => 'Beginner Friendly',
            'icon' => '🌱'
        ],
        [
            'id' => 2,
            'plan_name' => 'Professional Plan',
            'daily_rate' => 0.12,
            'min_amount' => 1000,
            'max_amount' => 4999,
            'duration' => 20,
            'description' => 'Most popular choice',
            'features' => ['Higher daily returns', 'Priority customer support', 'Advanced analytics dashboard', 'Referral bonus boosts', 'Investment history tracking'],
            'is_featured' => true,
            'is_active' => true,
            'badge' => 'Most Popular',
            'icon' => '🚀'
        ],
        [
            'id' => 3,
            'plan_name' => 'Enterprise Plan',
            'daily_rate' => 0.15,
            'min_amount' => 5000,
            'max_amount' => null,
            'duration' => 30,
            'description' => 'Maximum returns',
            'features' => ['Maximum daily returns', 'Dedicated account manager', 'VIP support channel', 'Custom investment strategies', 'Exclusive market insights'],
            'is_featured' => false,
            'is_active' => true,
            'badge' => 'VIP Level',
            'icon' => '💎'
        ]
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Plans - TLS Wallet</title>
    <meta name="description" content="Discover TLS Wallet's high-yield investment plans. Earn daily returns on your USDT investments with our secure TRON-based platform.">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/public.css">
    <link rel="stylesheet" href="css/invest-info.css">
    <link rel="manifest" href="manifest.webmanifest">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="home.php" style="text-decoration: none; color: white;">
                    <h2>TLS Wallet</h2>
                </a>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="index.php" class="nav-link">Home</a>
                <a href="invest-info.php" class="nav-link active">Invest</a>
                <a href="faq.php" class="nav-link">FAQ</a>
                <?php if ($isLoggedIn): ?>
                    <a href="user/dashboard.php" class="nav-link">Dashboard</a>
                    <a href="login.php?logout=1" class="btn btn-outline nav-btn">Logout</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-primary nav-btn">Login</a>
                <?php endif; ?>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="invest-hero">
        <div class="container">
            <div class="hero-content">
                <h1>High-Yield Investment Plans</h1>
                <p class="hero-subtitle">Grow your USDT with our secure, profitable investment opportunities on the TRON blockchain</p>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number"><?php echo !empty($investmentPlans) ? number_format(max(array_column($investmentPlans, 'daily_rate')) * 100, 2) : 15; ?>%</div>
                        <div class="stat-label">Max Daily Return</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number"><?php echo !empty($investmentPlans) ? max(array_column($investmentPlans, 'duration')) : 30; ?></div>
                        <div class="stat-label">Days Duration</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number"><?php echo !empty($investmentPlans) ? min(array_column($investmentPlans, 'min_amount')) : 100; ?></div>
                        <div class="stat-label">Min Investment</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Investment Calculator -->
    <section class="calculator-section">
        <div class="container">
            <div class="calculator-card">
                <h2>Investment Calculator</h2>
                <p>Calculate your potential earnings with our investment plans</p>
                
                <div class="calculator">
                    <div class="calc-input-group">
                        <label for="investAmount">Investment Amount (USDT)</label>
                        <input type="number" id="investAmount" min="100" step="10" value="1000" placeholder="Enter amount">
                    </div>
                    
                    <div class="calc-input-group">
                        <label for="investPlan">Select Plan</label>
                        <select id="investPlan">
                            <?php foreach ($investmentPlans as $plan): ?>
                                <?php if ($plan['is_active'] ?? true): // Only show active plans ?>
                                    <option value="<?php echo $plan['id']; ?>" 
                                            data-return="<?php echo ($plan['daily_rate'] ?? 0) * 100; ?>" 
                                            data-duration="<?php echo $plan['duration'] ?? 15; ?>"
                                            <?php echo ($plan['is_featured'] ?? false) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($plan['plan_name']); ?> (<?php echo number_format(($plan['daily_rate'] ?? 0) * 100, 2); ?>% daily, <?php echo $plan['duration'] ?? 15; ?> days)
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="calc-results">
                        <div class="result-item">
                            <span class="result-label">Daily Return:</span>
                            <span class="result-value" id="dailyReturn">120 USDT</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Total Return:</span>
                            <span class="result-value" id="totalReturn">2,400 USDT</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Total Received:</span>
                            <span class="result-value total" id="totalReceived">3,400 USDT</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Profit Percentage:</span>
                            <span class="result-value profit" id="profitPercentage">240%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Investment Plans -->
    <section class="plans-section">
        <div class="container">
            <div class="section-header">
                <h2>Our Investment Plans</h2>
                <p>Choose the plan that best fits your investment goals</p>
            </div>
            
            <div class="plans-grid">
                <?php foreach ($investmentPlans as $plan): 
                    $planClass = strtolower(str_replace(' ', '-', $plan['plan_name']));
                    $dailyReturn = ($plan['daily_rate'] ?? 0) * 100; // Convert to percentage
                    $duration = $plan['duration'] ?? 15;
                    $totalReturn = $dailyReturn * $duration;
                    $maxAmount = $plan['max_amount'] ? number_format($plan['max_amount']) . ' USDT' : 'Unlimited';
                    $exampleAmount = ($plan['min_amount'] ?? 100) * 2; // Example calculation
                    $exampleDaily = $exampleAmount * ($plan['daily_rate'] ?? 0);
                    $exampleTotal = $exampleDaily * $duration;
                    $exampleReceived = $exampleAmount + $exampleTotal;
                    $isPopular = $plan['is_featured'] ?? false;
                    $features = is_array($plan['features'] ?? null) ? $plan['features'] : [];
                ?>
                <!-- <?php echo htmlspecialchars($plan['plan_name']); ?> -->
                <div class="plan-card <?php echo $planClass; ?><?php echo $isPopular ? ' popular' : ''; ?>">
                    <?php if (isset($plan['badge']) || $isPopular): ?>
                        <div class="plan-badge <?php echo $isPopular ? 'popular-badge' : ''; ?>"><?php echo htmlspecialchars($plan['badge'] ?? ($isPopular ? 'Featured' : '')); ?></div>
                    <?php endif; ?>
                    <div class="plan-header">
                        <div class="plan-icon"><?php echo $plan['icon'] ?? '📊'; ?></div>
                        <h3><?php echo htmlspecialchars($plan['plan_name']); ?></h3>
                        <div class="plan-return"><?php echo number_format($dailyReturn, 2); ?>% <span>Daily</span></div>
                    </div>
                    
                    <div class="plan-details">
                        <div class="detail-row">
                            <span class="label">Minimum Investment:</span>
                            <span class="value"><?php echo number_format($plan['min_amount']); ?> USDT</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Maximum Investment:</span>
                            <span class="value"><?php echo $maxAmount; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Plan Duration:</span>
                            <span class="value"><?php echo $duration; ?> Days</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Total Return:</span>
                            <span class="value"><?php echo number_format($totalReturn, 1); ?>%</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Principal Return:</span>
                            <span class="value">✓ After <?php echo $duration; ?> days</span>
                        </div>
                    </div>
                    
                    <div class="plan-features">
                        <?php if (!empty($features)): ?>
                            <?php foreach ($features as $feature): ?>
                                <div class="feature">✓ <?php echo htmlspecialchars($feature); ?></div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="feature">✓ <?php echo htmlspecialchars($plan['description'] ?? 'Investment opportunity'); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="plan-example">
                        <h4>Example: <?php echo number_format($exampleAmount); ?> USDT Investment</h4>
                        <div class="example-breakdown">
                            <div class="breakdown-item">
                                <span>Daily Return:</span>
                                <span><?php echo number_format($exampleDaily, 2); ?> USDT</span>
                            </div>
                            <div class="breakdown-item">
                                <span>Total Profit:</span>
                                <span><?php echo number_format($exampleTotal, 2); ?> USDT</span>
                            </div>
                            <div class="breakdown-item total">
                                <span>You Receive:</span>
                                <span><?php echo number_format($exampleReceived, 2); ?> USDT</span>
                            </div>
                        </div>
                    </div>
                    
                    <?php 
                    $isActive = $plan['is_active'] ?? true; // Default to active if not specified
                    if ($isActive): ?>
                        <?php if ($isLoggedIn): ?>
                            <a href="user/invest.php" class="plan-btn<?php echo $isPopular ? ' featured' : ''; ?>">Start Investing</a>
                        <?php else: ?>
                            <a href="login.php" class="plan-btn<?php echo $isPopular ? ' featured' : ''; ?>">Join to Invest</a>
                        <?php endif; ?>
                    <?php else: ?>
                        <button class="plan-btn disabled" disabled>
                            <span>Currently Unavailable</span>
                        </button>
                        <div class="plan-status-note">
                            <small>This plan is temporarily unavailable. Please check back later or contact support.</small>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
                        <div class="feature">✓ Exclusive market insights</div>
                    </div>
                    
                    <div class="plan-example">
                        <h4>Example: 10,000 USDT Investment</h4>
                        <div class="example-breakdown">
                            <div class="breakdown-item">
                                <span>Daily Return:</span>
                                <span>1,500 USDT</span>
                            </div>
                            <div class="breakdown-item">
                                <span>Total Profit:</span>
                                <span>45,000 USDT</span>
                            </div>
                            <div class="breakdown-item total">
                                <span>You Receive:</span>
                                <span>55,000 USDT</span>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($isLoggedIn): ?>
                        <a href="user/invest.php" class="plan-btn">Start Investing</a>
                    <?php else: ?>
                        <a href="login.php" class="plan-btn">Join to Invest</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- How Investment Works -->
    <section class="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2>How Investment Works</h2>
                <p>Simple steps to start earning daily returns</p>
            </div>
            
            <div class="steps-timeline">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Choose Your Plan</h3>
                        <p>Select the investment plan that matches your budget and goals. Each plan offers different returns and durations.</p>
                    </div>
                </div>
                
                <div class="step-connector"></div>
                
                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Deposit USDT</h3>
                        <p>Transfer USDT to your TLS Wallet using our secure deposit system. Funds are protected with enterprise-grade security.</p>
                    </div>
                </div>
                
                <div class="step-connector"></div>
                
                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Investment Activation</h3>
                        <p>Your investment starts immediately and begins generating daily returns. Track progress in real-time through your dashboard.</p>
                    </div>
                </div>
                
                <div class="step-connector"></div>
                
                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>Daily Earnings</h3>
                        <p>Receive daily returns automatically credited to your wallet. Withdraw profits anytime or reinvest for compound growth.</p>
                    </div>
                </div>
                
                <div class="step-connector"></div>
                
                <div class="step-item">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>Principal Return</h3>
                        <p>Get your initial investment back at the end of the plan period, plus all the daily profits you've earned.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Risk Management -->
    <section class="risk-section">
        <div class="container">
            <div class="risk-content">
                <div class="risk-text">
                    <h2>Risk Management & Security</h2>
                    <p>Your investment security is our top priority. We implement multiple layers of protection:</p>
                    
                    <div class="security-features">
                        <div class="security-item">
                            <div class="security-icon">🛡️</div>
                            <div class="security-info">
                                <h3>Cold Storage</h3>
                                <p>95% of funds stored offline in secure cold wallets</p>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-icon">🔐</div>
                            <div class="security-info">
                                <h3>Multi-Signature</h3>
                                <p>Multiple approvals required for large transactions</p>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-icon">📊</div>
                            <div class="security-info">
                                <h3>Diversified Portfolio</h3>
                                <p>Investments spread across multiple DeFi protocols</p>
                            </div>
                        </div>
                        
                        <div class="security-item">
                            <div class="security-icon">🏦</div>
                            <div class="security-info">
                                <h3>Reserve Fund</h3>
                                <p>Emergency reserves to ensure payment stability</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="risk-warning">
                        <h3>⚠️ Investment Risk Warning</h3>
                        <p>Cryptocurrency investments carry inherent risks. Past performance does not guarantee future results. Only invest funds you can afford to lose. Please read our terms and conditions carefully before investing.</p>
                    </div>
                </div>
                
                <div class="risk-visual">
                    <div class="security-chart">
                        <div class="chart-item">
                            <div class="chart-bar" style="height: 95%">
                                <span class="chart-value">95%</span>
                            </div>
                            <span class="chart-label">Cold Storage</span>
                        </div>
                        <div class="chart-item">
                            <div class="chart-bar" style="height: 85%">
                                <span class="chart-value">85%</span>
                            </div>
                            <span class="chart-label">DeFi Yield</span>
                        </div>
                        <div class="chart-item">
                            <div class="chart-bar" style="height: 75%">
                                <span class="chart-value">75%</span>
                            </div>
                            <span class="chart-label">Liquidity Pool</span>
                        </div>
                        <div class="chart-item">
                            <div class="chart-bar" style="height: 65%">
                                <span class="chart-value">65%</span>
                            </div>
                            <span class="chart-label">Trading</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="investment-faq">
        <div class="container">
            <div class="section-header">
                <h2>Investment FAQ</h2>
                <p>Common questions about our investment plans</p>
            </div>
            
            <div class="faq-grid">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How are daily returns calculated?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Daily returns are calculated as a percentage of your initial investment amount. For example, with a 12% daily return on 1,000 USDT, you receive 120 USDT daily. Returns are processed automatically at 00:00 UTC daily.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I withdraw my investment early?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Investment principal is locked for the plan duration to ensure returns. However, you can withdraw your daily profits anytime. Early withdrawal of principal may be available with penalties - contact support for details.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What happens if I reinvest my profits?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Reinvesting profits creates compound growth. You can use daily earnings to start new investment plans, potentially increasing your overall returns significantly over time.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Are there any hidden fees?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>No hidden fees! The returns shown are what you receive. We only charge standard withdrawal fees (2 USDT) when you cash out. All plan details are transparent and clearly displayed.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Start Earning?</h2>
                <p>Join thousands of investors already earning daily returns with TLS Wallet</p>
                <div class="cta-stats">
                    <div class="cta-stat">
                        <div class="stat-number">$2.5M+</div>
                        <div class="stat-label">Total Invested</div>
                    </div>
                    <div class="cta-stat">
                        <div class="stat-number">10,000+</div>
                        <div class="stat-label">Active Investors</div>
                    </div>
                    <div class="cta-stat">
                        <div class="stat-number">$500K+</div>
                        <div class="stat-label">Paid Returns</div>
                    </div>
                </div>
                <?php if ($isLoggedIn): ?>
                    <a href="user/invest.php" class="btn btn-primary btn-large">Start Investing Now</a>
                <?php else: ?>
                    <a href="login.php" class="btn btn-primary btn-large">Create Account & Invest</a>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>TLS Wallet</h3>
                    <p>Your trusted partner in cryptocurrency investment and management.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <div class="footer-links">
                        <a href="index.php">Home</a>
                        <a href="invest-info.php">Investment Plans</a>
                        <a href="faq.php">FAQ</a>
                        <?php if (!$isLoggedIn): ?>
                            <a href="login.php">Login</a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <div class="footer-links">
                        <a href="faq.php">Help Center</a>
                        <a href="#contact">Contact Us</a>
                        <a href="#security">Security</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <div class="footer-links">
                        <a href="#terms">Terms of Service</a>
                        <a href="#privacy">Privacy Policy</a>
                        <a href="#disclaimer">Risk Disclaimer</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 TLS Wallet. All rights reserved.</p>
                <p><strong>Risk Warning:</strong> Cryptocurrency investments carry high risk. Only invest what you can afford to lose.</p>
            </div>
        </div>
    </footer>

    <script src="js/public.js"></script>
    <script src="js/invest-calculator.js"></script>
</body>
</html>
