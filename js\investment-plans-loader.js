// Dynamic investment plans loader
class InvestmentPlansLoader {
    constructor() {
        this.plans = [];
        this.isLoaded = false;
    }

    async loadPlans() {
        try {
            const response = await fetch('api.php?action=get_investment_plans');
            const data = await response.json();
            
            if (data.success && data.data) {
                this.plans = data.data;
                this.isLoaded = true;
                return this.plans;
            } else {
                console.error('Failed to load investment plans:', data.error);
                return this.getFallbackPlans();
            }
        } catch (error) {
            console.error('Error loading investment plans:', error);
            return this.getFallbackPlans();
        }
    }

    getFallbackPlans() {
        return [
            {
                id: 1,
                name: 'Starter Plan',
                daily_return: 8.0,
                minimum_amount: 100,
                maximum_amount: 999,
                duration_days: 15,
                description: 'Perfect for beginners',
                features: ['Daily Returns', 'Principal Back', '24/7 Support'],
                is_popular: false
            },
            {
                id: 2,
                name: 'Professional Plan',
                daily_return: 12.0,
                minimum_amount: 1000,
                maximum_amount: 4999,
                duration_days: 20,
                description: 'Most popular choice',
                features: ['Higher Returns', 'Priority Support', 'Advanced Analytics'],
                is_popular: true
            },
            {
                id: 3,
                name: 'Enterprise Plan',
                daily_return: 15.0,
                minimum_amount: 5000,
                maximum_amount: null,
                duration_days: 30,
                description: 'Maximum returns',
                features: ['Maximum Returns', 'Dedicated Manager', 'VIP Treatment'],
                is_popular: false
            }
        ];
    }

    getPlans() {
        return this.plans.length > 0 ? this.plans : this.getFallbackPlans();
    }

    getPlan(id) {
        return this.plans.find(plan => plan.id === id);
    }

    getPopularPlan() {
        return this.plans.find(plan => plan.is_popular) || this.plans[0];
    }
}

// Global instance
window.investmentPlansLoader = new InvestmentPlansLoader();

// Auto-load plans when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.investmentPlansLoader.loadPlans().then(plans => {
        // Trigger custom event for other scripts to listen to
        document.dispatchEvent(new CustomEvent('investmentPlansLoaded', {
            detail: { plans }
        }));
    });
});
